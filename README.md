# Log Management System (React + Vite)

## Prerequisite (Development)

| Module   | Version   |
|----------|-----------|
| Node     | 22.14.0  |
| Npm      | 10.8.2    |
| React    | 19.1.0    |
| Vite     | 7.0.4     |


---

## Environment Variables Setup

1. Copy the sample environment file:
   ```bash
   cp .env.sample .env
   ```
2. Edit `.env` and update environment-specific values as needed (API URLs, keys, etc.).
3. **Never commit your `.env` file to version control.**

---

## Running Project Locally

1. **Clone the repository:**
   ```bash
   git clone <REPOSITORY_URL>
   cd react-js
   git checkout dev
   ```
2. **Setup environment variables:**
   ```bash
   cp .env.sample .env
   vi .env   # Edit as needed
   ```
3. **Install dependencies:**
   ```bash
   npm install
   # or
   yarn
   ```
4. **Start the development server:**
   ```bash
   npm run dev
   # or
   yarn dev
   ```
5. **Open your browser:**
   - Visit [http://localhost:5173](http://localhost:5173)

---

## Deployment in QA Server

```bash
$ git clone <REPOSITORY_URL>
$ git checkout qa
$ cd react-js
$ cp .env.sample .env
$ vi .env               # (update environment details as needed)
$ npm install
$ npm run build         # or yarn build
# Deploy the contents of the 'dist' folder to the QA server
```

---

## Deployment in Master/Production Server

```bash
$ git clone <REPOSITORY_URL>
$ git checkout master
$ cd react-js
$ cp .env.sample .env
$ vi .env               # (update environment details as needed)
$ npm install
$ npm run build         # or yarn build
# Deploy the contents of the 'dist' folder to the production server
```

---

## Directory Structure

```
|-- public/
|   |-- favicon.svg
|   |-- vite.svg
|-- src/
|   |-- assets/
|   |-- components/
|   |   |-- CommonPagination.jsx
|   |   |-- CommonModal.jsx
|   |   |-- CommonSelect.jsx
|   |   |-- Navbar.jsx
|   |   |-- PrivateLayout.jsx
|   |-- features/
|   |   |-- auth/
|   |       |-- authApi.js
|   |       |-- authSlice.js
|   |-- pages/
|   |   |-- Dashboard/
|   |   |-- Projects/
|   |   |-- ManageUsers/
|   |   |-- ManageProfile/
|   |-- app/
|   |   |-- store.js
|   |-- routes/
|   |   |-- PrivateRoute.jsx
|   |   |-- PublicRoute.jsx
|   |-- styles/
|   |   |-- theme.scss
|   |   |-- global.css
|   |-- utils/
|   |   |-- alert.js
|   |   |-- toast.js
|   |-- App.jsx
|   |-- main.jsx
|-- .env.sample
|-- package.json
|-- README.md
```
