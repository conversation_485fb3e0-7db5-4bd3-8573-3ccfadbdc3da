{"name": "react-js", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "async-mutex": "^0.5.0", "chart.js": "^4.5.0", "clsx": "^2.1.0", "countries-and-timezones": "^3.8.0", "formik": "^2.4.6", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-data-table-component": "^7.7.0", "react-datepicker": "^8.4.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.3", "react-select": "^5.10.2", "react-toastify": "^11.0.5", "redux-persist": "^6.0.0", "sweetalert2": "^11.22.2", "tailwind-merge": "^2.6.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.17", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vite": "^7.0.4"}}