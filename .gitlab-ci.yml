stages:
  - build_dev
  - deploy_kube_dev
  - build_prod
  - deploy_kube_prod

build_dev:
  stage: build_dev
  tags:
    - docker-exec
  script:
    - echo "$DEV" >> .env
    - docker build -t harbor.indianic.com/logger/logger-dev-front:$CI_COMMIT_SHORT_SHA .
    - docker push harbor.indianic.com/logger/logger-dev-front:$CI_COMMIT_SHORT_SHA
  only:
    - dev

deploy_kube_dev:
  stage: deploy_kube_dev
  tags:
    - kube-prod-exec
  script:
    - kubectl set image deployment/logger-dev-front logger-dev-front=harbor.indianic.com/logger/logger-dev-front:$CI_COMMIT_SHORT_SHA -n logger
  only:
    - dev

build_prod:
  stage: build_prod
  tags:
    - docker-exec
  script:
    - echo "$PROD" >> .env
    - docker build -t harbor.indianic.com/logger/logger-prod-front:$CI_COMMIT_SHORT_SHA .
    - docker push harbor.indianic.com/logger/logger-prod-front:$CI_COMMIT_SHORT_SHA
  only:
    - master

deploy_kube_prod:
  stage: deploy_kube_prod
  tags:
    - kube-prod-exec
  script:
    - kubectl set image deployment/logger-prod-front logger-prod-front=harbor.indianic.com/logger/logger-prod-front:$CI_COMMIT_SHORT_SHA -n logger
  only:
    - master