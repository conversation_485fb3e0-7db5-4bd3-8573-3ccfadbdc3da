import configs from "../configs";

export default {
  API_URL: configs.REACT_APP_SERVER_URL,

  // Auth api url 
  LOGIN: "admin/login",

  // Project api urls
  PROJECT_LISTING: "project/listing",
  PROJECT_CREATE_UPDATE: "project",
  TOGGLE_PROJECT_FAVOURITE: "/project/favourite",
  DELETE_PROJECT: "project",
  GET_PROJECT_DETAILS: "/project/detail",
  GET_PROJECT_STATISTICS: "/project/statistics",
  PROJECT_LOGS_LISTING: "project-logs/listing",
  PROJECT_LOGS_USER_EMAILS: "project-logs/user-emails",
  PROJECT_LOGS_USER_ROLES: "project-logs/user-roles",
  PROJECT_ENVIRONMENTS: "project/apiKey/environments",
  GENERATE_API_KEY: "project/generate-api-key",
  API_KEY_LISTING: "project/apiKey/listing",
  DELETE_API_KEY: "project/apiKey/manual-log-deletion",
  COMMON_ENVIRONMENTS: "common/environments",
  PROJECT_LOG_DETAIL: "project-logs/detail",
};
