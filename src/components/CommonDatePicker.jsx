import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { cn } from '../utils/cn';

const CommonDatePicker = ({
  selected,
  onChange,
  placeholder = "Select date",
  minDate = null,
  maxDate = null,
  showTimeSelect = false,
  timeFormat = "HH:mm",
  timeIntervals = 15,
  dateFormat = showTimeSelect ? "dd/MM/yyyy HH:mm" : "dd/MM/yyyy",
  className = "",
  error = "",
  disabled = false,
  required = false,
  label = "",
  ...props
}) => {
  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <DatePicker
        selected={selected}
        onChange={onChange}
        placeholderText={placeholder}
        minDate={minDate}
        maxDate={maxDate}
        showTimeSelect={showTimeSelect}
        timeFormat={timeFormat}
        timeIntervals={timeIntervals}
        dateFormat={dateFormat}
        disabled={disabled}
        className={cn(
          "w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
          error 
            ? "border-red-300 focus:ring-red-500" 
            : "border-gray-300",
          disabled && "bg-gray-100 cursor-not-allowed",
          className
        )}
        wrapperClassName="w-full"
        calendarClassName="shadow-lg border border-gray-200 rounded-lg"
        {...props}
      />
      
      {error && (
        <p className="mt-1 text-xs text-red-600">{error}</p>
      )}
    </div>
  );
};

export default CommonDatePicker; 