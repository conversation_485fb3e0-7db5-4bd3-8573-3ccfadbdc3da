import React from 'react';
import { ApiPerformanceChart } from '../ReportsCharts';
import TimeFilter from '../TimeFilter';
import CommonDataTable from '../CommonDataTable';
import CommonPagination from '../CommonPagination';

const ApiPerformanceModalContent = ({
  selectedTimeFilter,
  onTimeFilterChange,
  selectedSortBy,
  onSortByChange,
  selectedTimezone,
  onTimezoneChange,
  currentPage,
  setCurrentPage,
  pageSize,
  handlePageSizeChange
}) => {
  // Define columns for the detailed metrics table
  const getDetailedMetricsColumns = () => [
    {
      name: 'Endpoint',
      selector: (row) => row.endpoint,
      cell: (row) => (
        <span className="font-mono text-xs text-gray-800">{row.endpoint}</span>
      ),
      sortable: true,
    },
    {
      name: 'Avg Response Time',
      selector: (row) => row.avgResponseTime,
      cell: (row) => (
        <span className="font-semibold text-gray-800">{row.avgResponseTime}</span>
      ),
      sortable: true,
    },
    {
      name: 'Success Rate',
      selector: (row) => row.successRate,
      cell: (row) => (
        <span className="font-semibold text-green-600">{row.successRate}</span>
      ),
      sortable: true,
    },
    {
      name: 'Status Codes',
      selector: (row) => row.statusCodes,
      cell: (row) => (
        <div className="flex flex-wrap gap-1">
          {row.statusCodes.map((status, index) => {
            const colorClass = status.code.startsWith('2') 
              ? 'bg-green-100 text-green-800 hover:bg-green-200'
              : status.code.startsWith('4')
              ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'  
              : 'bg-red-100 text-red-800 hover:bg-red-200';
            
            return (
              <span
                key={index}
                className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 border-transparent ${colorClass}`}
              >
                {status.code}: {status.count}
              </span>
            );
          })}
        </div>
      ),
    },
  ];

  // Sample data for the detailed metrics table (complete dataset)
  const allDetailedMetricsData = [
    {
      endpoint: '/api/users',
      avgResponseTime: '120ms',
      successRate: '98.5%',
      statusCodes: [
        { code: '200', count: 985 },
        { code: '500', count: 15 },
      ],
    },
    {
      endpoint: '/api/products',
      avgResponseTime: '245ms',
      successRate: '99.1%',
      statusCodes: [
        { code: '200', count: 1245 },
        { code: '404', count: 8 },
        { code: '500', count: 3 },
      ],
    },
    {
      endpoint: '/api/orders',
      avgResponseTime: '180ms',
      successRate: '97.8%',
      statusCodes: [
        { code: '200', count: 789 },
        { code: '401', count: 12 },
        { code: '500', count: 6 },
      ],
    },
    {
      endpoint: '/api/auth/login',
      avgResponseTime: '95ms',
      successRate: '96.2%',
      statusCodes: [
        { code: '200', count: 645 },
        { code: '401', count: 25 },
        { code: '500', count: 2 },
      ],
    },
    {
      endpoint: '/api/auth/logout',
      avgResponseTime: '45ms',
      successRate: '99.8%',
      statusCodes: [
        { code: '200', count: 890 },
        { code: '500', count: 2 },
      ],
    },
    {
      endpoint: '/api/payments',
      avgResponseTime: '320ms',
      successRate: '94.5%',
      statusCodes: [
        { code: '200', count: 567 },
        { code: '400', count: 18 },
        { code: '500', count: 15 },
      ],
    },
    {
      endpoint: '/api/search',
      avgResponseTime: '275ms',
      successRate: '98.9%',
      statusCodes: [
        { code: '200', count: 1120 },
        { code: '404', count: 12 },
        { code: '500', count: 1 },
      ],
    },
    {
      endpoint: '/api/analytics',
      avgResponseTime: '450ms',
      successRate: '97.1%',
      statusCodes: [
        { code: '200', count: 334 },
        { code: '403', count: 8 },
        { code: '500', count: 2 },
      ],
    },
    {
      endpoint: '/api/notifications',
      avgResponseTime: '85ms',
      successRate: '99.5%',
      statusCodes: [
        { code: '200', count: 756 },
        { code: '500', count: 4 },
      ],
    },
    {
      endpoint: '/api/upload',
      avgResponseTime: '1200ms',
      successRate: '92.3%',
      statusCodes: [
        { code: '200', count: 423 },
        { code: '413', count: 15 },
        { code: '500', count: 20 },
      ],
    },
    {
      endpoint: '/api/reports',
      avgResponseTime: '680ms',
      successRate: '96.8%',
      statusCodes: [
        { code: '200', count: 289 },
        { code: '403', count: 6 },
        { code: '500', count: 3 },
      ],
    },
    {
      endpoint: '/api/settings',
      avgResponseTime: '110ms',
      successRate: '99.3%',
      statusCodes: [
        { code: '200', count: 412 },
        { code: '401', count: 3 },
      ],
    },
  ];

  // Calculate pagination for API Performance detailed metrics table
  const totalPages = Math.ceil(allDetailedMetricsData.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedData = allDetailedMetricsData.slice(startIndex, endIndex);

  return (
    <div>
      <div className="flex justify-end items-center mb-6">
        <TimeFilter 
          selectedFilter={selectedTimeFilter}
          onFilterChange={onTimeFilterChange}
          selectedSortBy={selectedSortBy}
          onSortByChange={onSortByChange}
          selectedTimezone={selectedTimezone}
          onTimezoneChange={onTimezoneChange}
          showSortBy={true}
          showTimezone={true}
          showRequestMethod={false}
          showResponseCode={false}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div className="bg-white border border-gray-200 shadow-sm rounded-lg">
          <div className="p-5">
            <div className="text-xs text-gray-500 mb-2 font-medium">Total Requests</div>
            <div className="text-3xl font-bold leading-tight">1,234,567</div>
          </div>
        </div>
        <div className="bg-white border border-gray-200 shadow-sm rounded-lg">
          <div className="p-5">
            <div className="text-xs text-gray-500 mb-2 font-medium">Avg Response Time</div>
            <div className="text-3xl font-bold leading-tight">245ms</div>
          </div>
        </div>
        <div className="bg-white border border-gray-200 shadow-sm rounded-lg">
          <div className="p-5">
            <div className="text-xs text-gray-500 mb-2 font-medium">Success Rate</div>
            <div className="text-3xl font-bold leading-tight">99.2%</div>
          </div>
        </div>
      </div>

      <div className="bg-white border border-gray-200 shadow-sm rounded-lg mb-6">
        <div className="bg-gray-50 border-b border-gray-200 px-5 py-4">
          <h6 className="text-gray-800 font-semibold m-0">Performance Over Time</h6>
        </div>
        <div className="p-5">
          <div style={{ height: '300px' }}>
            <ApiPerformanceChart />
          </div>
        </div>
      </div>

      <div className="bg-white border border-gray-200 shadow-sm rounded-lg">
        <div className="bg-gray-50 border-b border-gray-200 px-5 py-4">
          <h6 className="text-gray-800 font-semibold m-0">Detailed Metrics</h6>
        </div>
        <div className="overflow-hidden">
          <CommonDataTable
            columns={getDetailedMetricsColumns()}
            data={paginatedData}
            pagination={false}
            highlightOnHover={true}
            pointerOnHover={true}
            customStyles={{
              headRow: {
                style: {
                  backgroundColor: '#f8fafc',
                  borderBottom: '1px solid #e2e8f0',
                },
              },
              headCells: {
                style: {
                  color: '#475569',
                  fontSize: '12px',
                  fontWeight: '600',
                  textTransform: 'uppercase',
                  letterSpacing: '0.05em',
                  padding: '16px',
                },
              },
              rows: {
                style: {
                  borderBottom: '1px solid #f1f5f9',
                  '&:hover': {
                    backgroundColor: '#f8fafc',
                  },
                },
              },
              cells: {
                style: {
                  padding: '16px',
                  fontSize: '14px',
                },
              },
            }}
          />
          
          <div className="border-t bg-gray-50 px-6 py-3 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">Show</span>
              <select
                className="px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={pageSize}
                onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
              <span className="text-sm text-gray-700">entries</span>
            </div>
            
            {totalPages > 1 && (
              <CommonPagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiPerformanceModalContent; 