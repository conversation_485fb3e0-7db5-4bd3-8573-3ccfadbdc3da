import React from 'react';
import { RequestVolumeChart } from '../ReportsCharts';
import TimeFilter from '../TimeFilter';

const RequestVolumeModalContent = ({
  selectedTimeFilter,
  onTimeFilterChange,
  selectedRequestMethod,
  onRequestMethodChange,
  selectedResponseCode,
  onResponseCodeChange
}) => {
  return (
    <div>
      {/* Filter Section */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-4 items-center justify-end">
          {/* Time Filter Component */}
          <TimeFilter 
              selectedFilter={selectedTimeFilter}
              onFilterChange={onTimeFilterChange}
              selectedRequestMethod={selectedRequestMethod}
              onRequestMethodChange={onRequestMethodChange}
              selectedResponseCode={selectedResponseCode}
              onResponseCodeChange={onResponseCodeChange}
              showSortBy={false}
              showTimezone={false}
              showRequestMethod={true}
              showResponseCode={true}
            />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white border border-gray-200 shadow-sm rounded-lg">
          <div className="p-5">
            <div className="text-xs text-gray-500 mb-2 font-medium">Total Requests</div>
            <div className="text-3xl font-bold leading-tight">2,345</div>
          </div>
        </div>
        <div className="bg-white border border-gray-200 shadow-sm rounded-lg">
          <div className="p-5">
            <div className="text-xs text-gray-500 mb-2 font-medium">Peak Volume</div>
            <div className="text-3xl font-bold leading-tight">125/min</div>
          </div>
        </div>
        <div className="bg-white border border-gray-200 shadow-sm rounded-lg">
          <div className="p-5">
            <div className="text-xs text-gray-500 mb-2 font-medium">Average Volume</div>
            <div className="text-3xl font-bold leading-tight">45/min</div>
          </div>
        </div>
        <div className="bg-white border border-gray-200 shadow-sm rounded-lg">
          <div className="p-5">
            <div className="text-xs text-gray-500 mb-2 font-medium">Time Range</div>
            <div className="text-3xl font-bold leading-tight">1h</div>
          </div>
        </div>
      </div>

      <div className="bg-white border border-gray-200 shadow-sm rounded-lg mb-6">
        <div className="bg-gray-50 border-b border-gray-200 px-5 py-4">
          <h6 className="text-gray-800 font-semibold m-0 flex items-center">
            📊 Request Volume by Time
          </h6>
        </div>
        <div className="p-5">
          <div style={{ height: '400px' }}>
            <RequestVolumeChart 
              onClick={(event) => {
                // Handle chart click - you can access click coordinates and data
                const rect = event.currentTarget.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;
                console.log('Chart clicked at:', { x, y });
                
                // You can add specific logic here based on the click position
                // For example, show details for a specific time period
                alert(`Chart clicked! Time period details would be shown here.`);
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestVolumeModalContent; 