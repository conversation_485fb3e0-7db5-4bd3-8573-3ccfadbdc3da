import React from 'react';
import { ResponseCodeChart } from '../ReportsCharts';
import TimeFilter from '../TimeFilter';
import CommonDataTable from '../CommonDataTable';

const ResponseCodeModalContent = ({
  selectedTimeFilter,
  onTimeFilterChange,
  selectedRequestMethod,
  onRequestMethodChange,
  selectedResponseCode,
  onResponseCodeChange
}) => {
  // Response code data
  const responseCodeData = [
    { id: 1, statusCode: '200', count: 700, percentage: '58.3%', color: 'bg-green-500 text-white' },
    { id: 2, statusCode: '201', count: 80, percentage: '6.7%', color: 'bg-green-500 text-white' },
    { id: 3, statusCode: '204', count: 30, percentage: '2.5%', color: 'bg-green-500 text-white' },
    { id: 4, statusCode: '301', count: 20, percentage: '1.7%', color: 'bg-cyan-500 text-white' },
    { id: 5, statusCode: '302', count: 15, percentage: '1.3%', color: 'bg-cyan-500 text-white' },
    { id: 6, statusCode: '400', count: 90, percentage: '7.5%', color: 'bg-orange-500 text-white' },
    { id: 7, statusCode: '401', count: 25, percentage: '2.1%', color: 'bg-orange-500 text-white' },
    { id: 8, statusCode: '403', count: 18, percentage: '1.5%', color: 'bg-orange-500 text-white' },
    { id: 9, statusCode: '404', count: 60, percentage: '5.0%', color: 'bg-orange-500 text-white' },
    { id: 10, statusCode: '409', count: 10, percentage: '0.8%', color: 'bg-orange-500 text-white' },
    { id: 11, statusCode: '422', count: 8, percentage: '0.7%', color: 'bg-orange-500 text-white' },
    { id: 12, statusCode: '429', count: 7, percentage: '0.6%', color: 'bg-orange-500 text-white' },
    { id: 13, statusCode: '500', count: 60, percentage: '5.0%', color: 'bg-red-500 text-white' },
    { id: 14, statusCode: '502', count: 20, percentage: '1.7%', color: 'bg-red-500 text-white' },
    { id: 15, statusCode: '503', count: 30, percentage: '2.5%', color: 'bg-red-500 text-white' },
    { id: 16, statusCode: '504', count: 25, percentage: '2.1%', color: 'bg-red-500 text-white' },
  ];

  // Response code table columns
  const responseCodeColumns = [
    {
      name: 'Status Code',
      selector: (row) => row.statusCode,
      cell: (row) => (
        <span className={`px-2 py-1 rounded font-semibold text-sm ${row.color}`}>
          {row.statusCode}
        </span>
      ),
      sortable: true,
      width: '40%',
    },
    {
      name: 'Count',
      selector: (row) => row.count,
      cell: (row) => (
        <span className="text-gray-800 font-medium">
          {row.count.toLocaleString()}
        </span>
      ),
      sortable: true,
      center: true,
      width: '30%',
    },
    {
      name: '% of Total',
      selector: (row) => row.percentage,
      cell: (row) => (
        <span className="text-blue-600 font-semibold">
          {row.percentage}
        </span>
      ),
      sortable: true,
      center: true,
      width: '30%',
    },
  ];

  return (
    <div>
      {/* Filter Section */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-4 items-center justify-end">
          {/* Time Filter Component */}
          <TimeFilter 
              selectedFilter={selectedTimeFilter}
              onFilterChange={onTimeFilterChange}
              selectedRequestMethod={selectedRequestMethod}
              onRequestMethodChange={onRequestMethodChange}
              selectedResponseCode={selectedResponseCode}
              onResponseCodeChange={onResponseCodeChange}
              showSortBy={false}
              showTimezone={false}
              showRequestMethod={true}
              showResponseCode={true}
            />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-5 gap-4 mb-6">
        <div className="text-center">
          <div className="text-3xl font-bold text-blue-600 mb-1">1,200</div>
          <div className="text-xs text-gray-500 uppercase tracking-wide">TOTAL REQUESTS</div>
        </div>
        <div className="text-center">
          <div className="text-3xl font-bold text-blue-600 mb-1">30</div>
          <div className="text-xs text-gray-500 uppercase tracking-wide">MINUTES PERIOD</div>
        </div>
        <div className="text-center">
          <div className="text-3xl font-bold text-blue-600 mb-1">40</div>
          <div className="text-xs text-gray-500 uppercase tracking-wide">REQUESTS/MIN</div>
        </div>
        <div className="text-center">
          <div className="text-3xl font-bold text-blue-600 mb-1">15</div>
          <div className="text-xs text-gray-500 uppercase tracking-wide">UNIQUE ENDPOINTS</div>
        </div>
        <div className="text-center">
          <div className="text-3xl font-bold text-blue-600 mb-1">8</div>
          <div className="text-xs text-gray-500 uppercase tracking-wide">UNIQUE USERS</div>
        </div>
      </div>

      {/* Chart and Table Section */}
      <div className="bg-white border border-gray-200 shadow-sm rounded-lg mb-6">
        <div className="bg-gray-50 border-b border-gray-200 px-5 py-4">
          <h6 className="text-gray-800 font-semibold m-0 flex items-center">
            📊 Response Code Analysis
          </h6>
        </div>
        <div className="p-5">
          {/* Chart Section */}
          <div className="mb-6">
            <div style={{ height: '400px' }}>
              <ResponseCodeChart />
            </div>
          </div>
          
          {/* Data Table Section */}
          <div>
            <CommonDataTable
              columns={responseCodeColumns}
              data={responseCodeData}
              pagination={false}
              highlightOnHover
              pointerOnHover
              customStyles={{
                headRow: {
                  style: {
                    backgroundColor: '#f3f4f6',
                    borderBottom: '1px solid #e5e7eb',
                  },
                },
                headCells: {
                  style: {
                    color: '#374151',
                    fontSize: '14px',
                    fontWeight: '600',
                    textTransform: 'uppercase',
                    padding: '12px 16px',
                  },
                },
                rows: {
                  style: {
                    borderBottom: '1px solid #f3f4f6',
                    '&:hover': {
                      backgroundColor: '#f9fafb',
                    },
                  },
                },
                cells: {
                  style: {
                    padding: '12px 16px',
                    fontSize: '14px',
                  },
                },
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResponseCodeModalContent; 