import React from 'react';
import { HttpMethodChart } from '../ReportsCharts';
import TimeFilter from '../TimeFilter';
import CommonDataTable from '../CommonDataTable';

const HttpMethodModalContent = ({
  selectedTimeFilter,
  onTimeFilterChange
}) => {
  // HTTP method data
  const httpMethodData = [
    { id: 1, method: 'GET', requestCount: 320, percentage: '44.4%' },
    { id: 2, method: 'POST', requestCount: 210, percentage: '29.2%' },
    { id: 3, method: 'PUT', requestCount: 60, percentage: '8.3%' },
    { id: 4, method: 'DELETE', requestCount: 30, percentage: '4.2%' },
    { id: 5, method: 'PATCH', requestCount: 15, percentage: '2.1%' },
    { id: 6, method: 'OPTIONS', requestCount: 10, percentage: '1.4%' },
    { id: 7, method: 'HEAD', requestCount: 5, percentage: '0.7%' },
  ];

  // HTTP method table columns
  const httpMethodColumns = [
    {
      name: 'HTTP Method',
      selector: (row) => row.method,
      cell: (row) => (
        <span className="font-semibold text-gray-800">
          {row.method}
        </span>
      ),
      sortable: true,
      width: '40%',
    },
    {
      name: 'Request Count',
      selector: (row) => row.requestCount,
      cell: (row) => (
        <span className="text-gray-800 font-medium">
          {row.requestCount.toLocaleString()}
        </span>
      ),
      sortable: true,
      center: true,
      width: '30%',
    },
    {
      name: '% of Total',
      selector: (row) => row.percentage,
      cell: (row) => (
        <span className="text-blue-600 font-semibold">
          {row.percentage}
        </span>
      ),
      sortable: true,
      center: true,
      width: '30%',
    },
  ];

  return (
    <div>
      <div className="flex justify-end items-center mb-6">
        <TimeFilter 
          selectedFilter={selectedTimeFilter}
          onFilterChange={onTimeFilterChange}
          showSortBy={false}
          showTimezone={false}
          showRequestMethod={false}
          showResponseCode={false}
        />
      </div>

      <div className="bg-white border border-gray-200 shadow-sm rounded-lg mb-6">
        <div className="bg-gray-50 border-b border-gray-200 px-5 py-4">
          <h6 className="text-gray-800 font-semibold m-0 flex items-center">
            📊 HTTP Method Distribution
          </h6>
        </div>
        <div className="p-5">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Chart Section */}
            <div style={{ height: '400px' }}>
              <HttpMethodChart />
            </div>
            
            {/* Data Table Section */}
            <div>
              <div className="mb-4">
                {/* HTTP Method Distribution Table */}
                <CommonDataTable
                  columns={httpMethodColumns}
                  data={httpMethodData}
                  pagination={false}
                  highlightOnHover
                  pointerOnHover
                  customStyles={{
                    headRow: {
                      style: {
                        backgroundColor: '#f3f4f6',
                        borderBottom: '1px solid #e5e7eb',
                      },
                    },
                    headCells: {
                      style: {
                        color: '#374151',
                        fontSize: '14px',
                        fontWeight: '600',
                        textTransform: 'uppercase',
                        padding: '12px 16px',
                      },
                    },
                    rows: {
                      style: {
                        borderBottom: '1px solid #f3f4f6',
                        '&:hover': {
                          backgroundColor: '#f9fafb',
                        },
                      },
                    },
                    cells: {
                      style: {
                        padding: '12px 16px',
                        fontSize: '14px',
                      },
                    },
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HttpMethodModalContent; 