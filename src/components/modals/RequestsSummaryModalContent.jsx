import React, { useState } from 'react';
import CommonDataTable from '../CommonDataTable';
import TimeFilter from '../TimeFilter';

const RequestsSummaryModalContent = () => {
  const [activeTab, setActiveTab] = useState('IP');
  const [searchIP, setSearchIP] = useState('');
  const [responseCodeFilter, setResponseCodeFilter] = useState('All');
  
  const [selectedTimeFilter, setSelectedTimeFilter] = useState({
    label: 'Last 24 hours',
    value: '24h'
  });
  const [selectedSortBy, setSelectedSortBy] = useState('request_volume');
  const [selectedTimezone, setSelectedTimezone] = useState('America/New_York');

  // Handler functions for TimeFilter
  const onTimeFilterChange = (filter) => {
    setSelectedTimeFilter(filter);
    console.log('Time filter changed:', filter);
  };

  const onSortByChange = (sortBy) => {
    setSelectedSortBy(sortBy);
    console.log('Sort by changed:', sortBy);
  };

  const onTimezoneChange = (timezone) => {
    setSelectedTimezone(timezone);
    console.log('Timezone changed:', timezone);
  };

  // Sample data for IP Address tab
  const ipAddressData = [
    {
      id: 1,
      ipAddress: '***********',
      firstSeen: '10:00:15',
      lastSeen: '10:25:30',
      totalRequests: 5,
      statusCodes: [
        { code: '200', count: 2, color: 'text-green-600' },
        { code: '400', count: 1, color: 'text-orange-600' },
        { code: '500', count: 2, color: 'text-red-600' }
      ]
    },
    {
      id: 2,
      ipAddress: '*********',
      firstSeen: '09:45:22',
      lastSeen: '11:30:45',
      totalRequests: 12,
      statusCodes: [
        { code: '200', count: 10, color: 'text-green-600' },
        { code: '400', count: 2, color: 'text-orange-600' }
      ]
    },
    {
      id: 3,
      ipAddress: '************',
      firstSeen: '08:15:10',
      lastSeen: '08:45:33',
      totalRequests: 8,
      statusCodes: [
        { code: '200', count: 6, color: 'text-green-600' },
        { code: '500', count: 2, color: 'text-red-600' }
      ]
    },
    {
      id: 4,
      ipAddress: '************',
      firstSeen: '14:20:05',
      lastSeen: '15:10:18',
      totalRequests: 15,
      statusCodes: [
        { code: '200', count: 12, color: 'text-green-600' },
        { code: '400', count: 1, color: 'text-orange-600' },
        { code: '500', count: 2, color: 'text-red-600' }
      ]
    },
    {
      id: 5,
      ipAddress: '*************',
      firstSeen: '16:30:12',
      lastSeen: '16:35:28',
      totalRequests: 3,
      statusCodes: [
        { code: '400', count: 2, color: 'text-orange-600' },
        { code: '500', count: 1, color: 'text-red-600' }
      ]
    },
    {
      id: 6,
      ipAddress: '*************',
      firstSeen: '12:05:30',
      lastSeen: '13:45:15',
      totalRequests: 4,
      statusCodes: [
        { code: '200', count: 3, color: 'text-green-600' },
        { code: '400', count: 1, color: 'text-orange-600' }
      ]
    }
  ];

  // Sample data for Email Address tab
  const emailAddressData = [
    {
      id: 1,
      emailAddress: '<EMAIL>',
      firstSeen: '10:00:15',
      lastSeen: '10:25:30',
      totalRequests: 5,
      statusCodes: [
        { code: '200', count: 2, color: 'text-green-600' },
        { code: '400', count: 1, color: 'text-orange-600' },
        { code: '500', count: 2, color: 'text-red-600' }
      ]
    },
    {
      id: 2,
      emailAddress: '<EMAIL>',
      firstSeen: '09:30:45',
      lastSeen: '11:15:20',
      totalRequests: 18,
      statusCodes: [
        { code: '200', count: 15, color: 'text-green-600' },
        { code: '400', count: 2, color: 'text-orange-600' },
        { code: '500', count: 1, color: 'text-red-600' }
      ]
    },
    {
      id: 3,
      emailAddress: '<EMAIL>',
      firstSeen: '14:20:10',
      lastSeen: '16:45:35',
      totalRequests: 12,
      statusCodes: [
        { code: '200', count: 10, color: 'text-green-600' },
        { code: '400', count: 2, color: 'text-orange-600' }
      ]
    },
    {
      id: 4,
      emailAddress: '<EMAIL>',
      firstSeen: '13:15:25',
      lastSeen: '13:45:50',
      totalRequests: 7,
      statusCodes: [
        { code: '200', count: 4, color: 'text-green-600' },
        { code: '400', count: 2, color: 'text-orange-600' },
        { code: '500', count: 1, color: 'text-red-600' }
      ]
    },
    {
      id: 5,
      emailAddress: '<EMAIL>',
      firstSeen: '08:45:30',
      lastSeen: '17:30:15',
      totalRequests: 25,
      statusCodes: [
        { code: '200', count: 20, color: 'text-green-600' },
        { code: '400', count: 3, color: 'text-orange-600' },
        { code: '500', count: 2, color: 'text-red-600' }
      ]
    },
    {
      id: 6,
      emailAddress: '<EMAIL>',
      firstSeen: '15:10:40',
      lastSeen: '15:25:55',
      totalRequests: 3,
      statusCodes: [
        { code: '400', count: 2, color: 'text-orange-600' },
        { code: '500', count: 1, color: 'text-red-600' }
      ]
    }
  ];

  // Sample data for Role tab
  const roleData = [
    {
      id: 1,
      role: 'admin',
      firstSeen: '10:00:15',
      lastSeen: '10:25:30',
      totalRequests: 5,
      statusCodes: [
        { code: '200', count: 2, color: 'text-green-600' },
        { code: '400', count: 1, color: 'text-orange-600' },
        { code: '500', count: 2, color: 'text-red-600' }
      ]
    },
    {
      id: 2,
      role: 'user',
      firstSeen: '09:15:20',
      lastSeen: '16:30:45',
      totalRequests: 32,
      statusCodes: [
        { code: '200', count: 28, color: 'text-green-600' },
        { code: '400', count: 3, color: 'text-orange-600' },
        { code: '500', count: 1, color: 'text-red-600' }
      ]
    },
    {
      id: 3,
      role: 'moderator',
      firstSeen: '11:45:10',
      lastSeen: '15:20:25',
      totalRequests: 14,
      statusCodes: [
        { code: '200', count: 12, color: 'text-green-600' },
        { code: '400', count: 1, color: 'text-orange-600' },
        { code: '500', count: 1, color: 'text-red-600' }
      ]
    },
    {
      id: 4,
      role: 'guest',
      firstSeen: '13:30:15',
      lastSeen: '14:15:40',
      totalRequests: 8,
      statusCodes: [
        { code: '200', count: 5, color: 'text-green-600' },
        { code: '400', count: 2, color: 'text-orange-600' },
        { code: '500', count: 1, color: 'text-red-600' }
      ]
    },
    {
      id: 5,
      role: 'developer',
      firstSeen: '08:00:05',
      lastSeen: '17:45:30',
      totalRequests: 22,
      statusCodes: [
        { code: '200', count: 18, color: 'text-green-600' },
        { code: '400', count: 2, color: 'text-orange-600' },
        { code: '500', count: 2, color: 'text-red-600' }
      ]
    },
    {
      id: 6,
      role: 'api_client',
      firstSeen: '12:20:30',
      lastSeen: '12:35:45',
      totalRequests: 6,
      statusCodes: [
        { code: '200', count: 4, color: 'text-green-600' },
        { code: '400', count: 1, color: 'text-orange-600' }
      ]
    }
  ];

  // Get current data based on active tab
  const getCurrentData = () => {
    switch (activeTab) {
      case 'IP':
        return ipAddressData;
      case 'Email':
        return emailAddressData;
      case 'Role':
        return roleData;
      default:
        return ipAddressData;
    }
  };

  // Get table columns based on active tab
  const getColumns = () => {
    const baseColumns = [
      {
        name: activeTab === 'IP' ? 'IP Address' : activeTab === 'Email' ? 'Email Address' : 'Role',
        selector: (row) => activeTab === 'IP' ? row.ipAddress : activeTab === 'Email' ? row.emailAddress : row.role,
        cell: (row) => {
          const value = activeTab === 'IP' ? row.ipAddress : activeTab === 'Email' ? row.emailAddress : row.role;
          const colorClass = activeTab === 'Role' ? 'text-red-600' : 'text-green-600';
          return (
            <span className={`cursor-pointer hover:opacity-80 font-medium ${colorClass}`}>
              {value}
            </span>
          );
        },
        sortable: true,
        grow: activeTab === 'Email' ? 2 : 1,
      },
      {
        name: 'First Seen',
        selector: (row) => row.firstSeen,
        cell: (row) => (
          <span className="text-gray-700">
            {row.firstSeen}
          </span>
        ),
        sortable: true,
        center: true,
        maxWidth: '120px',
      },
      {
        name: 'Last Seen',
        selector: (row) => row.lastSeen,
        cell: (row) => (
          <span className="text-gray-700">
            {row.lastSeen}
          </span>
        ),
        sortable: true,
        center: true,
        maxWidth: '120px',
      },
      {
        name: 'Total Requests',
        selector: (row) => row.totalRequests,
        cell: (row) => (
          <span className="text-gray-800 font-semibold">
            {row.totalRequests}
          </span>
        ),
        sortable: true,
        center: true,
        maxWidth: '140px',
      },
      {
        name: 'Status Code Summary',
        selector: (row) => row.statusCodes,
        cell: (row) => (
          <div className="space-y-1">
            {row.statusCodes.map((status, idx) => (
              <div key={idx} className="text-sm">
                <span className={`font-semibold ${status.color}`}>
                  {status.code}:
                </span>
                <span className="ml-1 text-gray-700">
                  {status.count} requests
                </span>
              </div>
            ))}
          </div>
        ),
        sortable: false,
        grow: 2,
      },
    ];

    return baseColumns;
  };

  const responseCodeOptions = [
    { value: 'All', label: 'All' },
    { value: '2xx', label: '2xx' },
    { value: '4xx', label: '4xx' },
    { value: '5xx', label: '5xx' },
    { value: 'Custom', label: 'Custom' },
  ];

  return (
    <div>
      {/* Stats Cards */}
      <div className="grid grid-cols-3 gap-6 mb-8">
        <div className="text-center">
          <div className="text-5xl font-bold text-blue-600 mb-2">18</div>
          <div className="text-gray-500 text-sm font-medium">Total Records</div>
        </div>
        <div className="text-center">
          <div className="text-5xl font-bold text-blue-600 mb-2">141</div>
          <div className="text-gray-500 text-sm font-medium">Total Requests</div>
        </div>
        <div className="text-center">
          <div className="text-5xl font-bold text-blue-600 mb-2">3</div>
          <div className="text-gray-500 text-sm font-medium">Data Types</div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex space-x-8">
          {['IP', 'Email', 'Role'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab}
            </button>
          ))}
        </nav>
      </div>

      {/* Filter Section */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <span className="text-sm">
            {activeTab === 'IP' && '📍'}
            {activeTab === 'Email' && '📧'}
            {activeTab === 'Role' && '👤'}
          </span>
          <h3 className={`font-medium ${
            activeTab === 'IP' ? 'text-blue-600' : 
            activeTab === 'Email' ? 'text-green-600' : 
            'text-red-600'
          }`}>
            Requests Summary by {activeTab} {activeTab !== 'Role' ? 'Address' : ''}
          </h3>
        </div>
        
        <div className="flex justify-between items-start gap-4">
          {/* Left side - Search and Response Code filters */}
          <div className="flex gap-4">
            {/* Search by IP */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search by {activeTab}
              </label>
              <input
                type="text"
                value={searchIP}
                onChange={(e) => setSearchIP(e.target.value)}
                placeholder={`Enter ${activeTab} address...`}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Filter by Response Code */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Filter by Response Code
              </label>
              <select
                value={responseCodeFilter}
                onChange={(e) => setResponseCodeFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {responseCodeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Right side - Time Filter */}
          <div>
            <TimeFilter 
              selectedFilter={selectedTimeFilter}
              onFilterChange={onTimeFilterChange}
              showRequestMethod={false}
              showResponseCode={false}
              showSortBy={false}
              showTimezone={false}
            />
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div className="w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
        <CommonDataTable
          columns={getColumns()}
          data={getCurrentData()}
          pagination={false}
          highlightOnHover
          pointerOnHover
          customStyles={{
            table: {
              style: {
                width: '100%',
                minWidth: '100%',
              },
            },
            headRow: {
              style: {
                backgroundColor: '#f8fafc',
                borderBottom: '1px solid #e2e8f0',
                width: '100%',
              },
            },
            headCells: {
              style: {
                color: '#374151',
                fontSize: '14px',
                fontWeight: '600',
                padding: '16px 12px',
              },
            },
            rows: {
              style: {
                borderBottom: '1px solid #f1f5f9',
                width: '100%',
                '&:hover': {
                  backgroundColor: '#f8fafc',
                },
              },
            },
            cells: {
              style: {
                padding: '16px 12px',
                fontSize: '14px',
              },
            },
          }}
        />
      </div>
    </div>
  );
};

export default RequestsSummaryModalContent; 