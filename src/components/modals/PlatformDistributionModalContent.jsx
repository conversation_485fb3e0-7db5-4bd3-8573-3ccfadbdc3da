import React from 'react';
import { PlatformDistributionChart } from '../ReportsCharts';
import TimeFilter from '../TimeFilter';
import CommonDataTable from '../CommonDataTable';

const PlatformDistributionModalContent = ({
  selectedTimeFilter,
  onTimeFilterChange,
  selectedRequestMethod,
  onRequestMethodChange,
  selectedResponseCode,
  onResponseCodeChange
}) => {
  // Platform distribution data
  const platformData = [
    { id: 1, platform: 'Mobile', requestCount: 350, percentage: '36.8%', color: 'text-green-600' },
    { id: 2, platform: 'Android', requestCount: 200, percentage: '21.1%', color: 'text-green-600' },
    { id: 3, platform: 'iOS', requestCount: 150, percentage: '15.8%', color: 'text-lime-600' },
    { id: 4, platform: 'Web', requestCount: 600, percentage: '63.2%', color: 'text-blue-600' },
    { id: 5, platform: 'Chrome', requestCount: 400, percentage: '42.1%', color: 'text-indigo-600' },
    { id: 6, platform: 'Firefox', requestCount: 120, percentage: '12.6%', color: 'text-orange-600' },
    { id: 7, platform: 'Edge', requestCount: 80, percentage: '8.4%', color: 'text-red-600' },
  ];

  // Platform distribution table columns
  const platformColumns = [
    {
      name: 'Platform / Browser',
      selector: (row) => row.platform,
      cell: (row) => (
        <span className={`font-semibold ${row.color}`}>
          {row.platform}
        </span>
      ),
      sortable: true,
      width: '40%',
    },
    {
      name: 'Request Count',
      selector: (row) => row.requestCount,
      cell: (row) => (
        <span className="text-gray-800 font-medium">
          {row.requestCount.toLocaleString()}
        </span>
      ),
      sortable: true,
      center: true,
      width: '30%',
    },
    {
      name: '% of Total',
      selector: (row) => row.percentage,
      cell: (row) => (
        <span className="text-blue-600 font-semibold">
          {row.percentage}
        </span>
      ),
      sortable: true,
      center: true,
      width: '30%',
    },
  ];

  return (
    <div>
      {/* Filter Section */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-4 items-center justify-end">
          {/* Time Filter Component */}
          <TimeFilter 
              selectedFilter={selectedTimeFilter}
              onFilterChange={onTimeFilterChange}
              selectedRequestMethod={selectedRequestMethod}
              onRequestMethodChange={onRequestMethodChange}
              selectedResponseCode={selectedResponseCode}
              onResponseCodeChange={onResponseCodeChange}
              showSortBy={false}
              showTimezone={false}
              showRequestMethod={true}
              showResponseCode={true}
            />
        </div>
      </div>

      <div className="bg-white border border-gray-200 shadow-sm rounded-lg mb-6">
        <div className="bg-gray-50 border-b border-gray-200 px-5 py-4">
          <h6 className="text-gray-800 font-semibold m-0 flex items-center">
            📊 Platform Request Distribution
          </h6>
        </div>
        <div className="p-5">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Chart Section */}
            <div style={{ height: '400px' }}>
              <PlatformDistributionChart />
            </div>
            
            {/* Data Table Section */}
            <div>
              <div className="mb-4">
                {/* Platform Distribution Table */}
                <CommonDataTable
                  columns={platformColumns}
                  data={platformData}
                  pagination={false}
                  highlightOnHover
                  pointerOnHover
                  customStyles={{
                    headRow: {
                      style: {
                        backgroundColor: '#f3f4f6',
                        borderBottom: '1px solid #e5e7eb',
                      },
                    },
                    headCells: {
                      style: {
                        color: '#374151',
                        fontSize: '14px',
                        fontWeight: '600',
                        textTransform: 'uppercase',
                        padding: '12px 16px',
                      },
                    },
                    rows: {
                      style: {
                        borderBottom: '1px solid #f3f4f6',
                        '&:hover': {
                          backgroundColor: '#f9fafb',
                        },
                      },
                    },
                    cells: {
                      style: {
                        padding: '12px 16px',
                        fontSize: '14px',
                      },
                    },
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlatformDistributionModalContent; 