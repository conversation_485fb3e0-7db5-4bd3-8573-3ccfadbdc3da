import React, { useState } from 'react';
import { FaTimes } from 'react-icons/fa';

// Import separated modal content components
import ApiPerformanceModalContent from './modals/ApiPerformanceModalContent';
import RequestVolumeModalContent from './modals/RequestVolumeModalContent';
import HttpMethodModalContent from './modals/HttpMethodModalContent';
import PlatformDistributionModalContent from './modals/PlatformDistributionModalContent';
import ResponseCodeModalContent from './modals/ResponseCodeModalContent';
import RequestsSummaryModalContent from './modals/RequestsSummaryModalContent';

const ChartModal = ({ show, onHide, chartType, chartTitle }) => {
  // Get user's current timezone
  const getUserTimezone = () => {
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch (error) {
      return 'America/New_York'; // fallback
    }
  };

  // Get current date and time for default values
  const now = new Date();
  const currentDate = now.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  const currentTime = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  const userTimezone = getUserTimezone();
  
  // State management
  const [selectedTimeFilter, setSelectedTimeFilter] = useState({
    label: `from ${currentDate} ${currentTime} to ${currentDate} ${currentTime}`,
    value: 'custom',
    customDates: {
      startDate: currentDate,
      startTime: currentTime,
      endDate: currentDate,
      endTime: currentTime,
      timezone: userTimezone
    }
  });

  // Filter states
  const [selectedSortBy, setSelectedSortBy] = useState('throughput');
  const [selectedTimezone, setSelectedTimezone] = useState(userTimezone);
  const [selectedRequestMethod, setSelectedRequestMethod] = useState('POST');
  const [selectedResponseCode, setSelectedResponseCode] = useState('All');

  // Pagination state for API Performance detailed metrics table only
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Event handlers
  const handleTimeFilterChange = (filter) => {
    setSelectedTimeFilter(filter);
  };

  const handleSortByChange = (sortBy) => {
    setSelectedSortBy(sortBy);
  };

  const handleTimezoneChange = (timezone) => {
    setSelectedTimezone(timezone);
  };

  const handleRequestMethodChange = (method) => {
    setSelectedRequestMethod(method);
  };

  const handleResponseCodeChange = (code) => {
    setSelectedResponseCode(code);
  };

  // Reset page when page size changes
  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };

  // Render appropriate modal content based on chart type
  const renderModalContent = () => {
    const commonProps = {
      selectedTimeFilter,
      onTimeFilterChange: handleTimeFilterChange,
      selectedRequestMethod,
      onRequestMethodChange: handleRequestMethodChange,
      selectedResponseCode,
      onResponseCodeChange: handleResponseCodeChange,
    };

    switch (chartType) {
      case 'apiPerformance':
        return (
          <ApiPerformanceModalContent
            {...commonProps}
            selectedSortBy={selectedSortBy}
            onSortByChange={handleSortByChange}
            selectedTimezone={selectedTimezone}
            onTimezoneChange={handleTimezoneChange}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            pageSize={pageSize}
            handlePageSizeChange={handlePageSizeChange}
          />
        );

      case 'requestVolume':
        return <RequestVolumeModalContent {...commonProps} />;

      case 'httpMethod':
        return (
          <HttpMethodModalContent
            selectedTimeFilter={selectedTimeFilter}
            onTimeFilterChange={handleTimeFilterChange}
          />
        );

      case 'platformDistribution':
        return <PlatformDistributionModalContent {...commonProps} />;

      case 'responseCode':
        return <ResponseCodeModalContent {...commonProps} />;

      case 'requestsSummary':
        return <RequestsSummaryModalContent {...commonProps} />;

      default:
        return (
          <div>
            <h2 className="text-gray-800 text-2xl font-semibold mb-6">
              {chartTitle}
            </h2>
            
            <div className="bg-white rounded-lg border border-gray-200 h-96 flex items-center justify-center">
              <div className="text-gray-400 text-center">
                <div className="text-lg mb-2">📊</div>
                <div>Chart view for {chartTitle}</div>
              </div>
            </div>
          </div>
        );
    }
  };

  if (!show) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Full-screen modal */}
      <div className="flex items-center justify-center min-h-screen">
        <div className="w-full h-screen max-w-none m-0">
          <div className="border-none rounded-none h-screen">
            <div className="bg-blue-500 border-none px-5 py-3 relative">
              <h2 className="text-white text-xl font-semibold m-0">
                {chartTitle}
              </h2>
              <button
                onClick={onHide}
                className="absolute right-5 top-1/2 transform -translate-y-1/2 text-white no-underline p-2 border-none hover:text-gray-100 hover:bg-white hover:bg-opacity-10 rounded focus:shadow-none"
              >
                <FaTimes size={20} />
              </button>
            </div>

            <div className="px-8 py-5 bg-gray-50 h-[calc(100vh-64px)] overflow-y-auto">
              {renderModalContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChartModal; 
