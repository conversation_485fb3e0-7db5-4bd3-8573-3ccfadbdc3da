import React from 'react';
import DataTable from 'react-data-table-component';

const customStyles = {
  table: { 
    style: { 
      width: '100%',
      minWidth: '100%',
      maxWidth: '100%'
    } 
  },
  headCells: { 
    style: { 
      fontWeight: 600, 
      fontSize: 15 
    } 
  },
  rows: { 
    style: { 
      fontSize: 14,
      width: '100%'
    } 
  },
  tableWrapper: {
    style: {
      width: '100%'
    }
  }
};

const CommonDataTable = ({ columns, data, customStyles: propCustomStyles, ...rest }) => {
  // Merge custom styles with default styles
  const mergedStyles = propCustomStyles 
    ? {
        ...customStyles,
        ...propCustomStyles,
        table: {
          ...customStyles.table,
          ...propCustomStyles.table
        },
        headCells: {
          ...customStyles.headCells,
          ...propCustomStyles.headCells
        },
        rows: {
          ...customStyles.rows,
          ...propCustomStyles.rows
        }
      }
    : customStyles;

  return (
    <div style={{ width: '100%', overflow: 'auto' }}>
      <DataTable
        columns={columns}
        data={data}
        dense
        striped
        highlightOnHover
        responsive
        pagination
        customStyles={mergedStyles}
        {...rest}
      />
    </div>
  );
};

export default CommonDataTable; 