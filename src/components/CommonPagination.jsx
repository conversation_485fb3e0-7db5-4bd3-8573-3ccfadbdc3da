import React from 'react';
import { cn } from '../utils/cn';

const CommonPagination = ({ currentPage = 1, totalPages = 1, onPageChange, className = '', ...rest }) => {
  if (totalPages <= 1) return null;

  const generatePageNumbers = () => {
    const items = [];
    const maxVisiblePages = 5;
    
    // Always show first page
    items.push(
      <button
        key={1}
        className={cn(
          "px-3 py-2 text-sm border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors",
          1 === currentPage 
            ? "bg-blue-600 text-white border-blue-600 hover:bg-blue-700" 
            : "bg-white text-gray-700"
        )}
        onClick={() => onPageChange(1)}
      >
        1
      </button>
    );

    if (totalPages <= maxVisiblePages + 2) {
      // Show all pages if total is small
      for (let number = 2; number <= totalPages; number++) {
        items.push(
          <button
            key={number}
            className={cn(
              "px-3 py-2 text-sm border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors",
              number === currentPage 
                ? "bg-blue-600 text-white border-blue-600 hover:bg-blue-700" 
                : "bg-white text-gray-700"
            )}
            onClick={() => onPageChange(number)}
          >
            {number}
          </button>
        );
      }
    } else {
      // Complex pagination logic
      let startPage, endPage;

      if (currentPage <= 3) {
        // Near the beginning
        startPage = 2;
        endPage = Math.min(maxVisiblePages, totalPages - 1);
      } else if (currentPage >= totalPages - 2) {
        // Near the end
        startPage = Math.max(totalPages - maxVisiblePages + 1, 2);
        endPage = totalPages - 1;
      } else {
        // In the middle
        startPage = currentPage - 1;
        endPage = currentPage + 1;
      }

      // Add ellipsis before if needed
      if (startPage > 2) {
        items.push(
          <span 
            key="ellipsis-start" 
            className="px-3 py-2 text-sm text-gray-500 border border-gray-300 bg-white"
          >
            ...
          </span>
        );
      }

      // Add middle pages
      for (let number = startPage; number <= endPage; number++) {
        items.push(
          <button
            key={number}
            className={cn(
              "px-3 py-2 text-sm border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors",
              number === currentPage 
                ? "bg-blue-600 text-white border-blue-600 hover:bg-blue-700" 
                : "bg-white text-gray-700"
            )}
            onClick={() => onPageChange(number)}
          >
            {number}
          </button>
        );
      }

      // Add ellipsis after if needed
      if (endPage < totalPages - 1) {
        items.push(
          <span 
            key="ellipsis-end" 
            className="px-3 py-2 text-sm text-gray-500 border border-gray-300 bg-white"
          >
            ...
          </span>
        );
      }

      // Always show last page if it's not already shown
      if (totalPages > 1) {
        items.push(
          <button
            key={totalPages}
            className={cn(
              "px-3 py-2 text-sm border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors",
              totalPages === currentPage 
                ? "bg-blue-600 text-white border-blue-600 hover:bg-blue-700" 
                : "bg-white text-gray-700"
            )}
            onClick={() => onPageChange(totalPages)}
          >
            {totalPages}
          </button>
        );
      }
    }

    return items;
  };

  return (
    <nav className={cn("flex items-center justify-center", className)} {...rest}>
      <div className="flex items-center -space-x-px">
        <button
          className={cn(
            "px-3 py-2 text-sm border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors",
            currentPage === 1
              ? "bg-gray-100 text-gray-400 cursor-not-allowed"
              : "bg-white text-gray-700 hover:bg-gray-50"
          )}
          onClick={() => onPageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
        >
          Previous
        </button>
        {generatePageNumbers()}
        <button
          className={cn(
            "px-3 py-2 text-sm border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors",
            currentPage === totalPages
              ? "bg-gray-100 text-gray-400 cursor-not-allowed"
              : "bg-white text-gray-700 hover:bg-gray-50"
          )}
          onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages}
        >
          Next
        </button>
      </div>
    </nav>
  );
};

export default CommonPagination;