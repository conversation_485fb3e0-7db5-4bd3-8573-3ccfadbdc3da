import React, { useState, useEffect, useRef } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import {
  FaShieldAlt,
  FaTachometerAlt,
  FaProjectDiagram,
  FaUsers,
  FaUserCircle,
  FaUser,
  FaKey,
  FaSignOutAlt,
  FaBars,
  FaTimes,
  FaChevronDown,
  FaChevronUp,
} from "react-icons/fa";
import { useDispatch } from "react-redux";
import { logout } from "../features/auth/authSlice";
import { cn } from "../utils/cn";

const AppNavbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    dispatch(logout());
    localStorage.clear();
    sessionStorage.clear();
    navigate("/login");
  };

  const toggleMobileMenu = () => {
    setIsOpen(!isOpen);
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Check if current path matches the link
  const isActiveLink = (path) => {
    return location.pathname === path;
  };

  const navLinks = [
    {
      to: "/dashboard",
      icon: FaTachometerAlt,
      label: "Dashboard"
    },
    {
      to: "/projects",
      icon: FaProjectDiagram,
      label: "Projects"
    },
    {
      to: "/manage-users",
      icon: FaUsers,
      label: "Invited Users"
    }
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 shadow-lg border-b border-blue-500/30 overflow-visible">
      <div className="mx-auto px-4 sm:px-6 lg:px-8 overflow-visible">
        <div className="flex justify-between items-center h-16 overflow-visible">
          {/* Logo */}
          <Link
            to="/dashboard"
            className="flex items-center space-x-3 text-white font-bold text-lg hover:text-blue-200 transition-colors duration-200 group"
          >
            <div className="flex items-center justify-center w-10 h-10 bg-white/15 backdrop-blur-sm rounded-xl border border-white/30 group-hover:bg-white/25 transition-all duration-200">
              <FaShieldAlt className="text-lg text-white" />
            </div>
            <span className="hidden sm:block">Log Management System</span>
            <span className="sm:hidden">LMS</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-2">
            {navLinks.map((link) => {
              const Icon = link.icon;
              const isActive = isActiveLink(link.to);

              return (
                <Link
                  key={link.to}
                  to={link.to}
                  className={cn(
                    "flex items-center space-x-2 px-4 py-2 rounded-xl font-medium transition-all duration-200 relative group",
                    isActive
                      ? "bg-white/20 text-white shadow-lg backdrop-blur-sm"
                      : "text-blue-100 hover:bg-white/15 hover:text-white"
                  )}
                >
                  <Icon className="text-lg" />
                  <span>{link.label}</span>
                </Link>
              );
            })}
          </div>

          {/* User Dropdown */}
          <div className="hidden md:flex items-center !relative !overflow-visible" ref={dropdownRef}>
            <button
              onClick={toggleDropdown}
              className={cn(
                "flex items-center space-x-3 px-4 py-2 rounded-xl font-medium transition-all duration-200 focus:outline-none group",
                isDropdownOpen
                  ? "bg-white/20 text-white backdrop-blur-sm"
                  : "text-blue-100 hover:bg-white/15 hover:text-white"
              )}
            >
              <div className="flex items-center justify-center w-8 h-8 bg-white/25 rounded-full">
                <FaUserCircle className="text-lg text-white" />
              </div>
              <span>John Doe</span>
              <div className="transition-transform duration-200">
                {isDropdownOpen ? (
                  <FaChevronUp className="text-sm" />
                ) : (
                  <FaChevronDown className="text-sm" />
                )}
              </div>
            </button>

            {/* Dropdown Menu */}
            {isDropdownOpen && (
              <div className="!absolute !z-[9999] !overflow-visible right-0 top-full mt-3 w-48 animate-fade-in">
                {/* Arrow */}
                <div className="absolute -top-2 right-6 w-4 h-4 bg-white backdrop-blur-xl border border-gray-200/20 rotate-45"></div>

                {/* Dropdown Content */}
                <div className="bg-white backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/20 py-1 relative overflow-auto max-h-96 custom-scrollbar">
                  {/* Menu Items */}
                  <div className="py-0">
                    <Link
                      to="/manage-profile"
                      className="flex items-center space-x-2 px-3 py-2 text-gray-700 hover:bg-blue-50 transition-colors duration-200 group"
                      onClick={() => setIsDropdownOpen(false)}
                    >
                      <div className="flex items-center justify-center w-6 h-6 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors duration-200">
                        <FaUser className="text-xs text-blue-600" />
                      </div>
                      <p className="font-medium text-sm">Profile</p>
                    </Link>

                    <Link
                      to="/change-password"
                      className="flex items-center space-x-2 px-3 py-2 text-gray-700 hover:bg-blue-50 transition-colors duration-200 group"
                      onClick={() => setIsDropdownOpen(false)}
                    >
                      <div className="flex items-center justify-center w-6 h-6 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors duration-200">
                        <FaKey className="text-xs text-green-600" />
                      </div>
                      <p className="font-medium text-sm">Change Password</p>
                    </Link>
                  </div>

                  {/* Logout Section */}
                  <div className="border-t border-gray-200/20 pt-0">
                    <button
                      onClick={() => {
                        handleLogout();
                        setIsDropdownOpen(false);
                      }}
                      className="flex items-center space-x-2 px-3 py-2 text-red-600 hover:bg-red-50 transition-colors duration-200 w-full text-left group"
                    >
                      <div className="flex items-center justify-center w-6 h-6 bg-red-100 rounded-lg group-hover:bg-red-200 transition-colors duration-200">
                        <FaSignOutAlt className="text-xs text-red-600" />
                      </div>
                      <p className="font-medium text-sm">Logout</p>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <button
            onClick={toggleMobileMenu}
            className="md:hidden flex items-center justify-center w-10 h-10 rounded-xl bg-white/15 backdrop-blur-sm border border-white/30 text-white hover:bg-white/25 focus:outline-none transition-all duration-200"
          >
            {isOpen ? (
              <FaTimes className="text-lg" />
            ) : (
              <FaBars className="text-xl" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <div className="md:hidden bg-blue-800/95 backdrop-blur-md border-t border-blue-500/20">
          <div className="px-2 pt-2 pb-3 space-y-1">
            <Link
              to="/dashboard"
              className="flex items-center space-x-2 px-3 py-2 rounded-md text-white hover:bg-white/10 transition-all duration-200"
              onClick={() => setIsOpen(false)}
            >
              <FaTachometerAlt className="text-lg" />
              <span>Dashboard</span>
            </Link>
            <Link
              to="/projects"
              className="flex items-center space-x-2 px-3 py-2 rounded-md text-white hover:bg-white/10 transition-all duration-200"
              onClick={() => setIsOpen(false)}
            >
              <FaProjectDiagram className="text-lg" />
              <span>Projects</span>
            </Link>
            <Link
              to="/manage-users"
              className="flex items-center space-x-2 px-3 py-2 rounded-md text-white hover:bg-white/10 transition-all duration-200"
              onClick={() => setIsOpen(false)}
            >
              <FaUsers className="text-lg" />
              <span>Invited Users</span>
            </Link>

            {/* Mobile User Menu */}
            <div className="border-t border-blue-500/20 pt-2 mt-2">
              <Link
                to="/manage-profile"
                className="flex items-center space-x-2 px-3 py-2 text-blue-100 hover:bg-white/10 transition-all duration-200"
                onClick={() => setIsOpen(false)}
              >
                <FaUser className="text-sm" />
                <span>Profile</span>
              </Link>
              <Link
                to="#"
                className="flex items-center space-x-2 px-3 py-2 text-blue-100 hover:bg-white/10 transition-all duration-200"
                onClick={() => setIsOpen(false)}
              >
                <FaKey className="text-sm" />
                <span>Change Password</span>
              </Link>
              <button
                onClick={() => {
                  handleLogout();
                  setIsOpen(false);
                }}
                className="flex items-center space-x-2 px-3 py-2 text-red-300 hover:bg-red-500/20 transition-all duration-200 w-full text-left"
              >
                <FaSignOutAlt className="text-sm" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default AppNavbar;
