import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  Filler
} from 'chart.js';
import { Bar, Doughnut, Line } from 'react-chartjs-2';
import CommonDataTable from './CommonDataTable';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  Filler
);

// API End Point Performance Report
export const ApiPerformanceChart = ({ onClick }) => {
  const data = {
    labels: ['/api/login', '/api/items/123', '/api/items', '/api/items'],
    datasets: [
      {
        label: 'Avg Response Time (ms)',
        data: [1000, 400, 300, 250],
        backgroundColor: ['#dc3545', '#ffc107', '#007bff', '#28a745'],
        borderRadius: 4,
        barThickness: 40,
      }
    ]
  };

  const options = {
    indexAxis: 'y',
    responsive: true,
    maintainAspectRatio: false,
    onClick: onClick,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: '#000',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#ccc',
        borderWidth: 1,
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        max: 1200,
        ticks: {
          stepSize: 200,
          font: {
            size: 11
          }
        },
        grid: {
          color: '#e9ecef'
        }
      },
      y: {
        ticks: {
          font: {
            size: 11
          }
        },
        grid: {
          display: false
        }
      }
    }
  };

  return <Bar data={data} options={options} height={220} />;
};

// Request Volume by Time
export const RequestVolumeChart = ({ onClick }) => {
  const data = {
    labels: ['10:00-10:05', '10:05-10:10', '10:10-10:15', '10:15-10:20', '10:20-10:25', '10:25-10:30'],
    datasets: [
      {
        label: 'Request Volume',
        data: [1.0, 0.1, 1.0, 1.1, 0.1, 2.0],
        borderColor: '#4285f4',
        backgroundColor: 'rgba(66, 133, 244, 0.1)',
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#4285f4',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    onClick: onClick,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: '#000',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#ccc',
        borderWidth: 1,
      }
    },
    scales: {
      x: {
        ticks: {
          font: {
            size: 10
          }
        },
        grid: {
          color: '#e9ecef'
        }
      },
      y: {
        beginAtZero: true,
        max: 2.2,
        ticks: {
          stepSize: 0.2,
          font: {
            size: 11
          }
        },
        grid: {
          color: '#e9ecef'
        }
      }
    }
  };

  return <Line data={data} options={options} height={220} />;
};

// End Point Popularity
// export const EndPointPopularityChart = ({ onClick }) => {
//   const data = {
//     labels: ['/api/login (POST)', '/api/items (GET)', '/api/items/123 (GET)', '/api/items (POST)'],
//     datasets: [
//       {
//         label: 'Request Count',
//         data: [2.0, 1.0, 1.0, 1.0],
//         backgroundColor: ['#6366f1', '#10b981', '#10b981', '#6366f1'],
//         borderRadius: 4,
//         barThickness: 40,
//       }
//     ]
//   };

//   const options = {
//     responsive: true,
//     maintainAspectRatio: false,
//     onClick: onClick,
//     plugins: {
//       legend: {
//         display: false,
//       },
//       tooltip: {
//         backgroundColor: '#000',
//         titleColor: '#fff',
//         bodyColor: '#fff',
//         borderColor: '#ccc',
//         borderWidth: 1,
//       }
//     },
//     scales: {
//       x: {
//         beginAtZero: true,
//         max: 2.2,
//         ticks: {
//           stepSize: 0.2,
//           font: {
//             size: 11
//           }
//         },
//         grid: {
//           color: '#e9ecef'
//         }
//       },
//       y: {
//         ticks: {
//           font: {
//             size: 10
//           }
//         },
//         grid: {
//           display: false
//         }
//       }
//     }
//   };

//   return <Bar data={data} options={options} height={220} />;
// };

// HTTP Method Distribution
export const HttpMethodChart = ({ onClick }) => {
  const data = {
    datasets: [
      {
        data: [44.4, 29.2, 8.3, 4.2, 2.1, 1.4, 0.7],
        backgroundColor: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6', '#6b7280', '#14b8a6'],
        borderWidth: 0,
        cutout: '50%',
      }
    ],
    labels: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD']
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    onClick: onClick,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          pointStyle: 'circle',
          padding: 15,
          font: {
            size: 11
          }
        }
      },
      tooltip: {
        backgroundColor: '#000',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#ccc',
        borderWidth: 1,
      }
    }
  };

  return <Doughnut data={data} options={options} height={220} />;
};

// Platform Request Distribution
export const PlatformDistributionChart = ({ onClick }) => {
  const data = {
    datasets: [
      {
        data: [36.8, 63.2, 21.1, 15.8, 42.1, 12.6, 8.4],
        backgroundColor: ['#10b981', '#3b82f6', '#10b981', '#84cc16', '#6366f1', '#f59e0b', '#ef4444'],
        borderWidth: 0,
        cutout: '50%',
      }
    ],
    labels: ['Mobile', 'Web', 'Android', 'iOS', 'Chrome', 'Firefox', 'Edge']
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    onClick: onClick,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          pointStyle: 'circle',
          padding: 15,
          font: {
            size: 11
          }
        }
      },
      tooltip: {
        backgroundColor: '#000',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#ccc',
        borderWidth: 1,
      }
    }
  };

  return <Doughnut data={data} options={options} height={220} />;
};

// Response Code Analysis
export const ResponseCodeChart = ({ onClick }) => {
  const data = {
    labels: ['200', '201', '204', '301', '302', '400', '401', '403', '404', '500', '502', '503'],
    datasets: [
      {
        label: 'Response Count',
        data: [700, 50, 30, 25, 15, 80, 40, 20, 60, 45, 30, 25],
        backgroundColor: [
          '#10b981', '#10b981', '#10b981', '#f59e0b', '#f59e0b', 
          '#ef4444', '#ef4444', '#ef4444', '#ef4444', '#ef4444', '#ef4444', '#ef4444'
        ],
        borderRadius: 2,
        barThickness: 20,
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    onClick: onClick,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: '#000',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: '#ccc',
        borderWidth: 1,
      }
    },
    scales: {
      x: {
        ticks: {
          font: {
            size: 10
          }
        },
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        max: 800,
        ticks: {
          stepSize: 100,
          font: {
            size: 11
          }
        },
        grid: {
          color: '#e9ecef'
        }
      }
    }
  };

  return <Bar data={data} options={options} height={220} />;
};

// Requests Summary Table Component
export const RequestsSummaryTable = ({ onClick }) => {
  const summaryData = [
    {
      id: 1,
      ip: '***********',
      firstSeen: '10:00:15',
      lastSeen: '10:25:30',
      totalRequests: 5,
      statusSummary: {
        '200': 2,
        '400': 1,
        '500': 2
      }
    },
    {
      id: 2,
      ip: '*********',
      firstSeen: '09:45:22',
      lastSeen: '11:30:45',
      totalRequests: 12,
      statusSummary: {
        '200': 10,
        '400': 2
      }
    },
    {
      id: 3,
      ip: '************',
      firstSeen: '08:15:10',
      lastSeen: '08:45:33',
      totalRequests: 8,
      statusSummary: {
        '200': 6,
        '500': 2
      }
    }
  ];

  const columns = [
    {
      name: 'IP Address',
      selector: (row) => row.ip,
      cell: (row) => (
        <span className="text-blue-600 cursor-pointer hover:text-blue-800 font-medium">
          {row.ip}
        </span>
      ),
      sortable: true,
      width: '150px',
    },
    {
      name: 'First Seen',
      selector: (row) => row.firstSeen,
      cell: (row) => (
        <span className="text-gray-700 text-sm">
          {row.firstSeen}
        </span>
      ),
      sortable: true,
      width: '120px',
    },
    {
      name: 'Last Seen',
      selector: (row) => row.lastSeen,
      cell: (row) => (
        <span className="text-gray-700 text-sm">
          {row.lastSeen}
        </span>
      ),
      sortable: true,
      width: '120px',
    },
    {
      name: 'Total Requests',
      selector: (row) => row.totalRequests,
      cell: (row) => (
        <span className="text-gray-800 font-semibold">
          {row.totalRequests}
        </span>
      ),
      sortable: true,
      center: true,
      width: '120px',
    },
    {
      name: 'Status Code Summary',
      selector: (row) => row.statusSummary,
      cell: (row) => (
        <div className="flex flex-wrap gap-2">
          {Object.entries(row.statusSummary).map(([code, count], idx) => {
            const colorClass = code.startsWith('2') 
              ? 'text-green-600' 
              : code.startsWith('4') 
              ? 'text-orange-600' 
              : 'text-red-600';
            
            return (
              <span key={idx} className="text-xs">
                <span className={`font-semibold ${colorClass}`}>
                  {code}:
                </span>
                <span className="ml-1 text-gray-700">
                  {count} requests
                </span>
              </span>
            );
          })}
        </div>
      ),
      sortable: false,
      width: '250px',
    },
  ];

  return (
    <div onClick={onClick} className="cursor-pointer">
      <CommonDataTable
        columns={columns}
        data={summaryData}
        pagination={false}
        highlightOnHover
        pointerOnHover
        dense
        onRowClicked={onClick}
        customStyles={{
          headRow: {
            style: {
              backgroundColor: '#f8fafc',
              borderBottom: '1px solid #e2e8f0',
              minHeight: '40px',
            },
          },
          headCells: {
            style: {
              color: '#475569',
              fontSize: '11px',
              fontWeight: '600',
              textTransform: 'uppercase',
              letterSpacing: '0.05em',
              padding: '8px 12px',
            },
          },
          rows: {
            style: {
              borderBottom: '1px solid #f1f5f9',
              minHeight: '45px',
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: '#f8fafc',
              },
            },
          },
          cells: {
            style: {
              padding: '8px 12px',
              fontSize: '11px',
            },
          },
        }}
      />
    </div>
  );
};

// User Journey Analysis Component
export const UserJourneyAnalysis = () => {
  return (
            <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <img 
          src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='64' height='64' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='1.5'%3E%3Cpath d='M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z'/%3E%3Cpath d='m15 5 4 4'/%3E%3C/svg%3E"
          alt="User journey analysis"
          style={{ opacity: 0.6 }}
        />
        <div style={{ fontSize: '11px', color: '#6b7280', marginTop: '8px' }}>
          User journey analysis
        </div>
      </div>
    </div>
  );
}; 
