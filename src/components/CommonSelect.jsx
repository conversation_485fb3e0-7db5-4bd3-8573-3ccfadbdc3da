import React, { forwardRef } from 'react';
import Select from 'react-select';

const CommonSelect = forwardRef(({
  options = [],
  value,
  onChange,
  placeholder = 'Select...',
  isMulti = false,
  isClearable = true,
  className = '',
  ...rest
}, ref) => {
  return (
    <Select
      ref={ref}
      options={options}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      isMulti={isMulti}
      isClearable={isClearable}
      className={className}
      classNamePrefix="common-select"
      {...rest}
    />
  );
});

export default CommonSelect; 