import React, { useState, useRef, useMemo } from 'react';
import { FaClock, FaTimes } from 'react-icons/fa';
import ct from 'countries-and-timezones';
import Select from 'react-select';
import CommonDatePicker from './CommonDatePicker';

const TimeFilter = ({ 
  selectedFilter, 
  onFilterChange, 
  selectedSortBy, 
  onSortByChange, 
  selectedTimezone, 
  onTimezoneChange,
  selectedRequestMethod,
  onRequestMethodChange,
  selectedResponseCode,
  onResponseCodeChange,
  showRequestMethod = false,
  showResponseCode = false,
  showSortBy = true,
  showTimezone = true
}) => {
  const [showCustomModal, setShowCustomModal] = useState(false);
  
  // Get user's current timezone
  const getUserTimezone = () => {
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch (error) {
      return 'America/New_York'; // fallback
    }
  };

  const [customDates, setCustomDates] = useState({
    startDateTime: new Date(),
    endDateTime: new Date(),
    timezone: getUserTimezone()
  });
  const buttonRef = useRef(null);

  // Sort by options
  const sortByOptions = [
    { value: 'throughput', label: 'Throughput (calls per minute)' },
    { value: 'response_time', label: 'Response Time' },
    { value: 'error_rate', label: 'Error Rate' },
    { value: 'request_volume', label: 'Request Volume' },
    { value: 'success_rate', label: 'Success Rate' },
  ];

  // Request Method options
  const requestMethodOptions = [
    { value: 'GET', label: 'GET' },
    { value: 'POST', label: 'POST' },
    { value: 'PUT', label: 'PUT' },
    { value: 'DELETE', label: 'DELETE' },
    { value: 'PATCH', label: 'PATCH' },
  ];

  // Response Code options (using same approach as logs section)
  const apiStatusCodes = [
    100, 101, 102,
    200, 201, 202, 203, 204, 205, 206, 207, 208, 226,
    300, 301, 302, 303, 304, 305, 307, 308,
    400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410,
    411, 412, 413, 414, 415, 416, 417, 418, 421, 422, 423, 424, 425, 426, 428, 429, 431, 451,
    500, 501, 502, 503, 504, 505, 506, 507, 508, 510, 511
  ];

  const responseCodeOptions = [
    { value: 'All', label: 'All' },
    ...apiStatusCodes.map((code) => ({
      value: code.toString(),
      label: code.toString(),
    }))
  ];

  // Generate timezone options from countries-and-timezones package
  const timezoneOptions = useMemo(() => {
    const timezones = ct.getAllTimezones();
    const options = [];

    // Add all timezones alphabetically from the package
    const allTimezoneEntries = Object.entries(timezones)
      .sort(([a], [b]) => a.localeCompare(b));

    allTimezoneEntries.forEach(([tz, timezone]) => {
      const offsetHours = timezone.utcOffset / 60;
      const offsetString = offsetHours >= 0 ? `+${offsetHours}` : `${offsetHours}`;
      const label = tz === 'UTC' ? 'UTC (Coordinated Universal Time)' : 
                   `${tz.replace(/_/g, ' ')} (UTC${offsetString})`;
      options.push({
        value: tz,
        label: label,
        // Add extra properties for better searching
        searchTerms: `${tz} ${tz.replace(/_/g, ' ')} ${timezone.name || ''} UTC${offsetString}`
      });
    });

    return options;
  }, []);

  // Get timezone abbreviation for display
  const getTimezoneAbbreviation = (timezoneId) => {
    try {
      if (!timezoneId) return 'UTC';
      const timezone = ct.getTimezone(timezoneId);
      if (timezone && timezone.abbreviation) {
        return timezone.abbreviation;
      }
      // Fallback: extract timezone abbreviation from the timezone ID
      if (timezoneId === 'UTC') return 'UTC';
      // For timezone IDs like "America/New_York", try to get a short form
      const parts = timezoneId.split('/');
      return parts[parts.length - 1].replace(/_/g, ' ').substring(0, 6) || 'UTC';
    } catch (error) {
      return 'UTC';
    }
  };

  // Handle timezone select change
  const handleTimezoneChange = (selectedOption) => {
    setCustomDates(prev => ({
      ...prev,
      timezone: selectedOption ? selectedOption.value : getUserTimezone(),
    }));
  };

  // Custom styles for react-select (compact for modal)
  const customSelectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: '32px',
      border: '1px solid #d1d5db',
      borderRadius: '4px',
      fontSize: '12px',
      fontWeight: '400',
      boxShadow: state.isFocused ? '0 0 0 2px rgba(59, 130, 246, 0.1)' : 'none',
      borderColor: state.isFocused ? '#3b82f6' : '#d1d5db',
      '&:hover': {
        borderColor: state.isFocused ? '#3b82f6' : '#9ca3af'
      },
      transition: 'all 0.2s ease-in-out'
    }),
    option: (provided, state) => ({
      ...provided,
      fontSize: '12px',
      fontWeight: '400',
      backgroundColor: state.isSelected 
        ? '#3b82f6' 
        : state.isFocused 
        ? '#f3f4f6' 
        : 'white',
      color: state.isSelected ? 'white' : '#374151',
      padding: '6px 8px'
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
      maxHeight: '200px',
      fontSize: '12px'
    }),
    menuList: (provided) => ({
      ...provided,
      maxHeight: '180px'
    }),
    placeholder: (provided) => ({
      ...provided,
      color: '#9ca3af',
      fontSize: '12px'
    }),
    singleValue: (provided) => ({
      ...provided,
      color: '#374151',
      fontSize: '12px'
    }),
    dropdownIndicator: (provided) => ({
      ...provided,
      padding: '4px'
    }),
    clearIndicator: (provided) => ({
      ...provided,
      padding: '4px'
    })
  };

  // Timezone-specific styles (blue theme for timezone)
  const timezoneSelectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: '32px',
      border: '1px solid #3b82f6',
      borderRadius: '6px',
      fontSize: '14px',
      backgroundColor: '#dbeafe',
      boxShadow: state.isFocused ? '0 0 0 2px rgba(59, 130, 246, 0.1)' : 'none',
      borderColor: state.isFocused ? '#3b82f6' : '#3b82f6',
      '&:hover': {
        borderColor: '#3b82f6'
      }
    }),
    option: (provided, state) => ({
      ...provided,
      fontSize: '14px',
      backgroundColor: state.isSelected 
        ? '#3b82f6' 
        : state.isFocused 
        ? '#f3f4f6' 
        : 'white',
      color: state.isSelected ? 'white' : '#374151',
      padding: '6px 8px'
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
      maxHeight: '200px',
      fontSize: '14px'
    }),
    menuList: (provided) => ({
      ...provided,
      maxHeight: '180px'
    }),
    placeholder: (provided) => ({
      ...provided,
      color: '#1e40af',
      fontSize: '14px'
    }),
    singleValue: (provided) => ({
      ...provided,
      color: '#1e40af',
      fontSize: '14px'
    }),
    dropdownIndicator: (provided) => ({
      ...provided,
      padding: '2px',
      color: '#1e40af'
    }),
    clearIndicator: (provided) => ({
      ...provided,
      padding: '2px',
      color: '#1e40af'
    })
  };

  const handleOptionSelect = (option) => {
    if (option.value === 'custom') {
      // Keep modal open for custom selection
      return;
    } else {
      // For preset options, apply immediately and close
      onFilterChange(option);
      setShowCustomModal(false);
    }
  };

  // Handle custom apply with proper date formatting
  const handleCustomApply = () => {
    try {
      const startDate = customDates.startDateTime.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
      const startTime = customDates.startDateTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      const endDate = customDates.endDateTime.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
      const endTime = customDates.endDateTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      
      const customOption = {
        label: `from ${startDate} ${startTime} to ${endDate} ${endTime}`,
        value: 'custom',
        customDates: {
          startDate,
          startTime,
          endDate,
          endTime
        }
      };
      onFilterChange(customOption);
      setShowCustomModal(false);
    } catch (error) {
      console.error('Error formatting custom date:', error);
      // Fallback to simple display
      const customOption = {
        label: 'Custom time range',
        value: 'custom',
        customDates: {
          startDate: 'Custom',
          startTime: '',
          endDate: 'Range',
          endTime: ''
        }
      };
      onFilterChange(customOption);
      setShowCustomModal(false);
    }
  };

  const handleCancel = () => {
    setShowCustomModal(false);
  };

  const resetToNow = () => {
    const now = new Date();
    setCustomDates({
      startDateTime: now,
      endDateTime: now,
      timezone: getUserTimezone()
    });
  };

  const getDisplayText = () => {
    if (selectedFilter && selectedFilter.value === 'custom') {
      return selectedFilter.label;
    }
    if (selectedFilter) {
      return selectedFilter.label;
    }
    // Default display without timezone
    try {
      const startDate = customDates.startDateTime.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
      const startTime = customDates.startDateTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      const endDate = customDates.endDateTime.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
      const endTime = customDates.endDateTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      return `from ${startDate} ${startTime} to ${endDate} ${endTime}`;
    } catch (error) {
      console.error('Error displaying date:', error);
      return 'Select time range';
    }
  };

  return (
    <div className="relative inline-block">
      {/* Filter Controls */}
      <div className="flex items-center gap-3">
        {/* Sort By Filter */}
        {showSortBy && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 font-medium">Sort by</span>
            <div className="min-h-[34px] bg-white border border-gray-300 rounded-md text-sm text-black px-3 py-1.5 hover:border-gray-400 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
              <Select
                value={sortByOptions.find(option => option.value === (selectedSortBy || 'throughput'))}
                onChange={(selectedOption) => onSortByChange && onSortByChange(selectedOption ? selectedOption.value : 'throughput')}
                options={sortByOptions}
                className="react-select-container"
                classNamePrefix="react-select"
                isSearchable={false}
                placeholder="Select sort option..."
                styles={{
                  control: (provided) => ({
                    ...provided,
                    border: 'none',
                    boxShadow: 'none',
                    backgroundColor: 'transparent',
                    minHeight: 'auto',
                    cursor: 'pointer'
                  }),
                  valueContainer: (provided) => ({
                    ...provided,
                    padding: '0'
                  }),
                  input: (provided) => ({
                    ...provided,
                    margin: '0',
                    padding: '0'
                  }),
                  singleValue: (provided) => ({
                    ...provided,
                    margin: '0',
                    color: '#000000'
                  }),
                  placeholder: (provided) => ({
                    ...provided,
                    margin: '0',
                    color: '#6b7280'
                  }),
                  indicatorsContainer: (provided) => ({
                    ...provided,
                    padding: '0'
                  }),
                  dropdownIndicator: (provided) => ({
                    ...provided,
                    padding: '4px',
                    color: '#6b7280',
                    '&:hover': {
                      color: '#374151'
                    }
                  }),
                  indicatorSeparator: () => ({
                    display: 'none'
                  }),
                  menu: (provided) => ({
                    ...provided,
                    zIndex: 9999,
                    marginTop: '4px'
                  }),
                  menuList: (provided) => ({
                    ...provided,
                    padding: '0'
                  }),
                  option: (provided, state) => ({
                    ...provided,
                    backgroundColor: state.isSelected 
                      ? '#3b82f6' 
                      : state.isFocused 
                      ? '#f3f4f6' 
                      : 'white',
                    color: state.isSelected ? 'white' : '#374151',
                    padding: '8px 12px',
                    cursor: 'pointer'
                  })
                }}
              />
            </div>
          </div>
        )}

        {/* Request Method Filter */}
        {showRequestMethod && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 font-medium">Method</span>
            <Select
              value={requestMethodOptions.find(option => option.value === selectedRequestMethod)}
              onChange={(selectedOption) => onRequestMethodChange && onRequestMethodChange(selectedOption ? selectedOption.value : null)}
              options={requestMethodOptions}
              styles={customSelectStyles}
              isSearchable={true}
              placeholder="Select method..."
              noOptionsMessage={({ inputValue }) => 
                inputValue ? `No method found for "${inputValue}"` : "No methods available"
              }
              formatOptionLabel={(option) => (
                <div>
                  <div className="font-normal">
                    {option.label}
                  </div>
                </div>
              )}
            />
          </div>
        )}

        {/* Response Code Filter */}
        {showResponseCode && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 font-medium">Response Code</span>
            <Select
              value={responseCodeOptions.find(option => option.value === selectedResponseCode)}
              onChange={(selectedOption) => onResponseCodeChange && onResponseCodeChange(selectedOption ? selectedOption.value : null)}
              options={responseCodeOptions}
              styles={customSelectStyles}
              isSearchable={true}
              placeholder="Select response code..."
              noOptionsMessage={({ inputValue }) => 
                inputValue ? `No response code found for "${inputValue}"` : "No response codes available"
              }
              formatOptionLabel={(option) => (
                <div>
                  <div className="font-normal">
                    {option.label}
                  </div>
                </div>
              )}
            />
          </div>
        )}

        {/* Timezone Filter */}
        {showTimezone && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 font-medium">Timezone</span>
            <div className="min-w-[200px]">
              <Select
                value={timezoneOptions.find(option => option.value === (selectedTimezone || getUserTimezone()))}
                onChange={(selectedOption) => onTimezoneChange && onTimezoneChange(selectedOption ? selectedOption.value : getUserTimezone())}
                options={timezoneOptions}
                styles={timezoneSelectStyles}
                isSearchable={true}
                placeholder="Search timezone..."
                noOptionsMessage={({ inputValue }) => 
                  inputValue ? `No timezone found for "${inputValue}"` : "No timezones available"
                }
                filterOption={(option, inputValue) => {
                  if (!inputValue) return true;
                  const searchValue = inputValue.toLowerCase();
                  return (
                    option.label.toLowerCase().includes(searchValue) ||
                    option.value.toLowerCase().includes(searchValue) ||
                    option.data?.searchTerms?.toLowerCase().includes(searchValue)
                  );
                }}
                formatOptionLabel={(option) => (
                  <div>
                    <div className="font-normal">
                      {getTimezoneAbbreviation(option.value)} - {option.label}
                    </div>
                  </div>
                )}
              />
            </div>
          </div>
        )}

        {/* Time Filter Button */}
        <button
          ref={buttonRef}
          onClick={() => setShowCustomModal(true)}
          className="min-w-[300px] bg-blue-50 border border-blue-200 text-blue-800 px-3 py-1.5 rounded-md hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-25 text-sm flex items-center gap-2"
        >
          <span className="text-blue-600">🕐</span>
          {getDisplayText()}
        </button>
      </div>

      {showCustomModal && (
        <>
          <div
            className="fixed inset-0 z-[1040] bg-transparent"
            onClick={() => setShowCustomModal(false)}
          />
          
          <div className="absolute top-full right-0 z-[1050] mt-1 bg-white border-none rounded-none shadow-[0_4px_20px_rgba(0,0,0,0.15)] w-[700px]">
            <div className="bg-white border-b border-gray-200 px-4 py-3">
              <div className="flex justify-end items-center w-full">
                <button 
                  onClick={() => setShowCustomModal(false)}
                  className="text-gray-500 px-2 py-1 text-base hover:text-gray-700 hover:bg-transparent focus:shadow-none"
                >
                  <FaTimes size={16} />
                </button>
              </div>
            </div>
            
            <div className="p-0 max-h-[500px]">
              <div className="flex min-h-[400px]">
                {/* Left Panel - Quick Options */}
                <div className="w-40 bg-gray-50 border-r border-gray-200">
                  <div>
                    <div 
                      className="relative flex items-center p-0 text-xs text-gray-600 cursor-pointer border-b border-gray-100 min-h-[36px] hover:bg-gray-100"
                      onClick={() => handleOptionSelect({ label: '30 minutes', value: '30m' })}
                    >
                      <div className="px-4 py-2.5 w-full">30 minutes</div>
                    </div>
                    <div 
                      className="relative flex items-center p-0 text-xs text-gray-600 cursor-pointer border-b border-gray-100 min-h-[36px] hover:bg-gray-100"
                      onClick={() => handleOptionSelect({ label: '60 minutes', value: '60m' })}
                    >
                      <div className="px-4 py-2.5 w-full">60 minutes</div>
                    </div>
                    <div 
                      className="relative flex items-center p-0 text-xs text-gray-600 cursor-pointer border-b border-gray-100 min-h-[36px] hover:bg-gray-100"
                      onClick={() => handleOptionSelect({ label: '3 hours', value: '3h' })}
                    >
                      <div className="px-4 py-2.5 w-full">3 hours</div>
                    </div>
                    <div 
                      className="relative flex items-center p-0 text-xs text-gray-600 cursor-pointer border-b border-gray-100 min-h-[36px] hover:bg-gray-100"
                      onClick={() => handleOptionSelect({ label: '6 hours', value: '6h' })}
                    >
                      <div className="px-4 py-2.5 w-full">6 hours</div>
                    </div>
                    <div 
                      className="relative flex items-center p-0 text-xs text-gray-600 cursor-pointer border-b border-gray-100 min-h-[36px] hover:bg-gray-100"
                      onClick={() => handleOptionSelect({ label: '12 hours', value: '12h' })}
                    >
                      <div className="px-4 py-2.5 w-full">12 hours</div>
                    </div>
                    <div 
                      className="relative flex items-center p-0 text-xs text-gray-600 cursor-pointer border-b border-gray-100 min-h-[36px] hover:bg-gray-100"
                      onClick={() => handleOptionSelect({ label: '24 hours', value: '24h' })}
                    >
                      <div className="px-4 py-2.5 w-full">24 hours</div>
                    </div>
                    <div 
                      className="relative flex items-center p-0 text-xs text-gray-600 cursor-pointer border-b border-gray-100 min-h-[36px] hover:bg-gray-100"
                      onClick={() => handleOptionSelect({ label: '3 days', value: '3d' })}
                    >
                      <div className="px-4 py-2.5 w-full">3 days</div>
                    </div>
                    <div 
                      className="relative flex items-center p-0 text-xs text-gray-600 cursor-pointer border-b border-gray-100 min-h-[36px] hover:bg-gray-100"
                      onClick={() => handleOptionSelect({ label: '7 days', value: '7d' })}
                    >
                      <div className="px-4 py-2.5 w-full">7 days</div>
                    </div>
                    <div 
                      className="relative flex items-center p-0 text-xs text-gray-600 cursor-pointer border-b border-gray-100 min-h-[36px] hover:bg-gray-100"
                      onClick={() => handleOptionSelect({ label: '1 month', value: '1M' })}
                    >
                      <div className="px-4 py-2.5 w-full">1 month</div>
                    </div>
                    <div 
                      className="relative flex items-center p-0 text-xs text-gray-600 cursor-pointer border-b border-gray-100 min-h-[36px] hover:bg-gray-100"
                      onClick={() => handleOptionSelect({ label: '3 months', value: '3M' })}
                    >
                      <div className="px-4 py-2.5 w-full">3 months</div>
                    </div>
                    <div className="relative flex items-center p-0 text-xs cursor-pointer min-h-[36px] bg-white text-gray-800 font-medium">
                      <div className="absolute left-0 top-0 bottom-0 w-0.5 bg-gradient-to-b from-orange-600 to-yellow-500"></div>
                      <div className="px-4 py-2.5 w-full">Set custom ▶</div>
                    </div>
                  </div>
                </div>

                {/* Right Panel - Custom Options */}
                <div className="flex-1 p-4 bg-white">
                  <div className="flex justify-between items-center mb-4">
                    <h6 className="text-xs font-semibold text-gray-500 uppercase tracking-wider m-0">
                      Set time range
                    </h6>
                    <button 
                      onClick={resetToNow}
                      className="bg-none border-none text-blue-500 text-xs cursor-pointer px-1.5 py-0.5 hover:bg-gray-50 rounded"
                    >
                      Reset to now
                    </button>
                  </div>
                  
                  <div className="mb-4">
                    <div className="flex gap-2 mb-2">
                      <CommonDatePicker
                        selected={customDates.startDateTime}
                        onChange={(date) => {
                          const newDate = new Date(customDates.startDateTime);
                          if (date) {
                            newDate.setFullYear(date.getFullYear());
                            newDate.setMonth(date.getMonth());
                            newDate.setDate(date.getDate());
                          }
                          setCustomDates({...customDates, startDateTime: newDate});
                        }}
                        showTimeSelect={false}
                        dateFormat="dd/MM/yyyy"
                        placeholder="Select start date"
                        className="text-xs h-8 py-1.5 flex-1"
                      />
                      <CommonDatePicker
                        selected={customDates.startDateTime}
                        onChange={(time) => {
                          const newDate = new Date(customDates.startDateTime);
                          if (time) {
                            newDate.setHours(time.getHours());
                            newDate.setMinutes(time.getMinutes());
                          }
                          setCustomDates({...customDates, startDateTime: newDate});
                        }}
                        showTimeSelect={true}
                        showTimeSelectOnly={true}
                        timeIntervals={15}
                        timeCaption="Time"
                        dateFormat="HH:mm"
                        placeholder="Select time"
                        className="text-xs h-8 py-1.5 w-24"
                      />
                    </div>
                    
                    <div className="flex gap-2 mb-2 border-b border-gray-200 pb-2">
                      <CommonDatePicker
                        selected={customDates.endDateTime}
                        onChange={(date) => {
                          const newDate = new Date(customDates.endDateTime);
                          if (date) {
                            newDate.setFullYear(date.getFullYear());
                            newDate.setMonth(date.getMonth());
                            newDate.setDate(date.getDate());
                          }
                          setCustomDates({...customDates, endDateTime: newDate});
                        }}
                        showTimeSelect={false}
                        dateFormat="dd/MM/yyyy"
                        placeholder="Select end date"
                        className="text-xs h-8 py-1.5 flex-1"
                        minDate={customDates.startDateTime}
                      />
                      <CommonDatePicker
                        selected={customDates.endDateTime}
                        onChange={(time) => {
                          const newDate = new Date(customDates.endDateTime);
                          if (time) {
                            newDate.setHours(time.getHours());
                            newDate.setMinutes(time.getMinutes());
                          }
                          setCustomDates({...customDates, endDateTime: newDate});
                        }}
                        showTimeSelect={true}
                        showTimeSelectOnly={true}
                        timeIntervals={15}
                        timeCaption="Time"
                        dateFormat="HH:mm"
                        placeholder="Select time"
                        className="text-xs h-8 py-1.5 w-24"
                      />
                    </div>
                
                    <div className="flex justify-end items-center gap-2.5 px-4 py-3">
                      <button 
                        onClick={handleCancel}
                        className="border border-gray-300 text-gray-600 bg-white text-xs px-4 py-1.5 rounded hover:bg-gray-50 hover:border-gray-400"
                      >
                        Cancel
                      </button>
                      <button 
                        onClick={handleCustomApply}
                        className="bg-blue-500 border border-blue-500 text-white text-xs px-4 py-1.5 rounded hover:bg-blue-600 hover:border-blue-600"
                      >
                        Apply
                      </button>
                    </div>
                  </div>
                  
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default TimeFilter; 
