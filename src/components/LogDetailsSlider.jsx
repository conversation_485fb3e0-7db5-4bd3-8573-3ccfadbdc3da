import React from 'react';
import { FaTimes } from 'react-icons/fa';
import { cn } from '../utils/cn';

const LogDetailsSlider = ({ show, onHide, logData }) => {
  if (!logData) return null;

  const getMethodColor = (method) => {
    switch (method?.toUpperCase()) {
      case 'GET': return 'bg-blue-100 text-blue-800';
      case 'POST': return 'bg-green-100 text-green-800';
      case 'PUT': return 'bg-yellow-100 text-yellow-800';
      case 'DELETE': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getResponseCodeColor = (code) => {
    if (code >= 200 && code < 300) return 'bg-green-100 text-green-800';
    if (code >= 300 && code < 400) return 'bg-blue-100 text-blue-800';
    if (code >= 400 && code < 500) return 'bg-yellow-100 text-yellow-800';
    if (code >= 500) return 'bg-red-100 text-red-800';
    return 'bg-gray-100 text-gray-800';
  };

  const getRoleBadgeColor = (role) => {
    if (role === 'Super Admin') return 'bg-purple-100 text-purple-800';
    if (role === 'Admin') return 'bg-green-100 text-green-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <>
      {/* Backdrop */}
      {show && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity"
          onClick={onHide}
        />
      )}
      
      {/* Slide-out Panel */}
      <div className={cn(
        "fixed top-0 right-0 h-full w-1/2 bg-white shadow-xl z-50 transform transition-transform duration-300 ease-in-out",
        show ? "translate-x-0" : "translate-x-full"
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Log Details</h2>
          <button
            onClick={onHide}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <FaTimes className="text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto h-full pb-20">
          {/* Meta Information */}
          <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
            <div className="flex items-center mb-3">
              <span className="text-blue-600 text-lg mr-2">📊</span>
              <h6 className="text-sm font-semibold text-blue-800">Meta Information</h6>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex">
                <span className="text-gray-600 w-24 flex-shrink-0">Time Stamp:</span>
                <span className="font-medium text-gray-900">{logData.timestamp || logData.timeStamp || '--'}</span>
              </div>
              <div className="flex">
                <span className="text-gray-600 w-24 flex-shrink-0">IP Address:</span>
                <span className="font-medium text-gray-900">{logData.ipAddress || logData.ip || '--'}</span>
              </div>
              <div className="flex">
                <span className="text-gray-600 w-24 flex-shrink-0">User Email:</span>
                <span className="font-medium text-gray-900">{logData.userEmail || logData.email || '--'}</span>
              </div>
              <div className="flex items-center">
                <span className="text-gray-600 w-24 flex-shrink-0">User Role:</span>
                <span className={cn(
                  "px-2 py-1 rounded-full text-xs font-medium",
                  getRoleBadgeColor(logData.userRole || logData.role)
                )}>
                  {logData.userRole || logData.role || '--'}
                </span>
              </div>
              <div className="flex">
                <span className="text-gray-600 w-24 flex-shrink-0">OS Version:</span>
                <span className="font-medium text-gray-900">{logData.osVersion || '--'}</span>
              </div>
              <div className="flex items-center">
                <span className="text-gray-600 w-24 flex-shrink-0">Device Type:</span>
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                  {logData.deviceType || 'Desktop'}
                </span>
              </div>
              <div className="flex">
                <span className="text-gray-600 w-24 flex-shrink-0">User Agent:</span>
                <span className="font-medium text-gray-900 text-xs break-all">{logData.userAgent || '--'}</span>
              </div>
            </div>
          </div>

          {/* Request-Response Information */}
          <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-4">
            <div className="flex items-center mb-3">
              <span className="text-green-600 text-lg mr-2">🔄</span>
              <h6 className="text-sm font-semibold text-green-800">Request-Response</h6>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center">
                <span className="text-gray-600 w-24 flex-shrink-0">HTTP Method:</span>
                <span className={cn(
                  "px-2 py-1 rounded-full text-xs font-medium",
                  getMethodColor(logData.httpMethod || logData.method)
                )}>
                  {logData.httpMethod || logData.method || '--'}
                </span>
              </div>
              <div className="flex items-center">
                <span className="text-gray-600 w-24 flex-shrink-0">Response Code:</span>
                <span className={cn(
                  "px-2 py-1 rounded-full text-xs font-medium",
                  getResponseCodeColor(logData.responseCode || logData.resCode)
                )}>
                  {logData.responseCode || logData.resCode || '--'}
                </span>
              </div>
              <div className="flex">
                <span className="text-gray-600 w-24 flex-shrink-0">End Point:</span>
                <span className="font-medium text-gray-900 font-mono text-xs break-all">
                  {logData.endPoint || logData.endpoint || '--'}
                </span>
              </div>
              <div className="flex">
                <span className="text-gray-600 w-24 flex-shrink-0">Page Route:</span>
                <span className="font-medium text-gray-900 font-mono text-xs break-all">
                  {logData.pageRoute || logData.route || '--'}
                </span>
              </div>
              <div className="flex">
                <span className="text-gray-600 w-24 flex-shrink-0">Query Parameter:</span>
                <span className="font-medium text-gray-900 text-xs break-all">
                  {typeof logData.queryParameter === 'object' 
                    ? JSON.stringify(logData.queryParameter) 
                    : logData.queryParameter || logData.query || '--'}
                </span>
              </div>
              <div className="flex">
                <span className="text-gray-600 w-24 flex-shrink-0">Request Body:</span>
                <span className="font-medium text-gray-900 text-xs break-all">
                  {typeof logData.requestBody === 'object' 
                    ? JSON.stringify(logData.requestBody) 
                    : logData.requestBody || logData.body || '--'}
                </span>
              </div>
              <div className="flex">
                <span className="text-gray-600 w-24 flex-shrink-0">Response Time:</span>
                <span className="font-medium text-gray-900">{logData.responseTime || logData.resTime || '--'}</span>
              </div>
            </div>
          </div>

          {/* Error Information */}
          <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
            <div className="flex items-center mb-3">
              <span className="text-red-600 text-lg mr-2">⚠️</span>
              <h6 className="text-sm font-semibold text-red-800">Error Information</h6>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex">
                <span className="text-gray-600 w-24 flex-shrink-0">Error Type:</span>
                <span className="font-medium text-gray-900">
                  {logData.errorType || '--'}
                </span>
              </div>
              <div className="flex">
                <span className="text-gray-600 w-24 flex-shrink-0">Error Message:</span>
                <span className="font-medium text-gray-900 text-xs break-all">
                  {logData.errorMessage || logData.errorMsg || '--'}
                </span>
              </div>
              <div className="flex">
                <span className="text-gray-600 w-24 flex-shrink-0">Error File:</span>
                <span className="font-medium text-gray-900 font-mono text-xs">
                  {logData.errorFile || '--'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default LogDetailsSlider; 