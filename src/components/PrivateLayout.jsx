import React from 'react';
import AppNavbar from './Navbar';

const PrivateLayout = ({ children }) => (
  <div className="min-h-screen bg-gray-50 flex flex-col">
    <header className="sticky top-0 z-40 bg-white shadow-sm border-b border-gray-200">
      <div className="h-16">
        <AppNavbar />
      </div>
    </header>
    <main className="flex-1 w-full px-6 py-6">
      {children}
    </main>
    <footer className="bg-white border-t border-gray-200 py-4">
      <div className="w-full px-6 text-center text-sm text-gray-600">
        © 2024 Log Management System
      </div>
    </footer>
  </div>
);

export default PrivateLayout; 