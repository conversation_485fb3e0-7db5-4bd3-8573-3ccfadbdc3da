import React, { useState, useEffect, useRef } from 'react';
import { FaChevronDown, FaTimes } from 'react-icons/fa';
import { cn } from '../utils/cn';

const AutocompleteSelect = ({ 
  value, 
  onChange, 
  options = [], 
  placeholder = "Type to search...", 
  isLoading = false, 
  onSearchChange,
  disabled = false,
  style = {},
  size = "sm"
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState(value || '');
  const dropdownRef = useRef(null);
  const inputRef = useRef(null);

  // Update search term when value changes externally
  useEffect(() => {
    setSearchTerm(value || '');
  }, [value]);

  // Handle input change
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    onChange(newValue);
    
    // Trigger search change with debounce
    if (onSearchChange) {
      onSearchChange(newValue);
    }
    
    // Open dropdown when typing
    if (newValue.length > 0) {
      setIsOpen(true);
    }
  };

  // Handle option selection
  const handleOptionSelect = (option) => {
    const selectedValue = typeof option === 'string' ? option : option.value || option;
    setSearchTerm(selectedValue);
    onChange(selectedValue);
    setIsOpen(false);
    inputRef.current?.blur();
  };

  // Handle clear
  const handleClear = (e) => {
    e.stopPropagation();
    setSearchTerm('');
    onChange('');
    setIsOpen(false);
    inputRef.current?.focus();
  };

  // Handle input focus
  const handleInputFocus = () => {
    if (options.length > 0) {
      setIsOpen(true);
    }
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filter options based on search term
  const filteredOptions = options.filter(option => {
    const optionText = typeof option === 'string' ? option : option.label || option.value || option;
    return optionText.toLowerCase().includes(searchTerm.toLowerCase());
  });

  return (
    <div ref={dropdownRef} className="relative" style={style}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            "w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
            size === "sm" ? "px-3 py-2 text-sm" : "px-4 py-3",
            searchTerm ? "pr-14" : "pr-9",
            disabled && "bg-gray-100 cursor-not-allowed"
          )}
        />
        
        {/* Clear button */}
        {searchTerm && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-7 top-1/2 -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <FaTimes size={10} />
          </button>
        )}
        
        {/* Dropdown arrow */}
        <div
          className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-500 cursor-pointer hover:text-gray-700"
          onClick={() => setIsOpen(!isOpen)}
        >
          <FaChevronDown size={10} />
        </div>
      </div>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 z-50 bg-white border border-gray-300 rounded-md shadow-sm max-h-48 overflow-y-auto mt-0.5">
          {isLoading ? (
            <div className="px-3 py-2 text-center text-gray-500">
              <div className="inline-block w-4 h-4 mr-2 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
              Loading...
            </div>
          ) : filteredOptions.length === 0 ? (
            <div className="px-3 py-2 text-gray-500 italic">
              {searchTerm ? 'No results found' : 'Start typing to search...'}
            </div>
          ) : (
            filteredOptions.map((option, index) => {
              const optionText = typeof option === 'string' ? option : option.label || option.value || option;
              return (
                <div
                  key={index}
                  onClick={() => handleOptionSelect(option)}
                  className={cn(
                    "px-3 py-2 cursor-pointer bg-white hover:bg-gray-50 transition-colors",
                    index < filteredOptions.length - 1 && "border-b border-gray-100"
                  )}
                >
                  {optionText}
                </div>
              );
            })
          )}
        </div>
      )}
    </div>
  );
};

export default AutocompleteSelect; 