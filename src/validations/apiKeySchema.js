import * as Yup from 'yup';

export const apiKeySchema = Yup.object().shape({
  keyName: Yup.string()
    .required('Key name is required')
    .min(3, 'Key name must be at least 3 characters')
    .max(50, 'Key name must be less than 50 characters')
    .matches(/^[a-zA-Z0-9\s\-_]+$/, 'Key name can only contain letters, numbers, spaces, hyphens, and underscores'),
  
  environment: Yup.string()
    .required('Environment is required'),
  
  expiryDate: Yup.date()
    .required('Expiry date is required')
    .min(new Date(), 'Expiry date must be in the future')
    .typeError('Please select a valid date'),
  
  logRetentionValue: Yup.number()
    .required('Log retention value is required')
    .positive('Log retention value must be positive')
    .integer('Log retention value must be a whole number')
    .min(1, 'Log retention value must be at least 1'),
  
  logRetentionUnit: Yup.string()
    .required('Log retention unit is required')
    .oneOf(['hours', 'days', 'months', 'years'], 'Invalid log retention unit'),
}); 