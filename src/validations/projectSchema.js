import * as Yup from 'yup';

export const projectSchema = Yup.object().shape({
  name: Yup.string()
    .required('Project name is required')
    .min(2, 'Project name must be at least 2 characters')
    .max(100, 'Project name must be less than 100 characters'),
  
  projectCode: Yup.string()
    .required('Project code is required')
    .min(2, 'Project code must be at least 2 characters')
    .max(10, 'Project code must be less than 10 characters'),
  
  projectType: Yup.array()
    .min(1, 'Please select at least one project type')
    .required('Project type is required'),
  
  // logRetention: Yup.number()
  //   .required('Log retention is required')
  //   .min(1, 'Log retention must be at least 1 day')
  //   .max(365, 'Log retention cannot exceed 365 days'),
  
  logEnabled: Yup.boolean(),
  
  status: Yup.boolean(),
}); 