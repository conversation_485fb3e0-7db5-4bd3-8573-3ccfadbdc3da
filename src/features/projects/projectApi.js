import { createApi } from "@reduxjs/toolkit/query/react";
import apiConfig from "../../configs/apiConfig";
import { REDUCER_PATHS } from "../../configs/routeConfig";
import { transformResponseHandler } from "../../utils/transformResponseHandler";
import { customFetchBase } from "../../utils/customFetchBase";

const projectApi = createApi({
    reducerPath: REDUCER_PATHS.PROJECT,
    baseQuery: customFetchBase,
    tagTypes: ['Project'],
    endpoints: (builder) => ({
        getProjectListing: builder.query({
            query: (params) => {
                const {
                    page = 1,
                    pagesize = 10,
                    searchText = "",
                    ...otherParams
                } = params;

                const body = {
                    page,
                    pagesize,
                    searchText
                };

                // Only add isFavourite and status if they exist in params
                if ('isFavourite' in params) {
                    body.isFavourite = params.isFavourite;
                }
                if ('status' in params) {
                    body.status = params.status;
                }

                console.log('API Body being sent:', body);

                return {
                    url: apiConfig.PROJECT_LISTING,
                    method: "POST",
                    body,
                };
            },
            transformResponse: (resp) => {
                // Handle the actual API response structure
                return {
                    projects: resp.data || [],
                    totalCount: resp.total || 0,
                    currentPage: resp.page || 1,
                    perPage: resp.perPage || 10,
                    favouriteCount: (resp.data || []).filter(project => project.isFavourite).length
                };
            },
            providesTags: ['Project'],
        }),

        createUpdateProject: builder.mutation({
            query: (data) => ({
                url: apiConfig.PROJECT_CREATE_UPDATE,
                method: "POST",
                body: data,
            }),
            transformResponse: (resp) => transformResponseHandler(resp),
            invalidatesTags: ['Project'],
        }),

        toggleProjectFavourite: builder.mutation({
            query: (data) => ({
                url: apiConfig.TOGGLE_PROJECT_FAVOURITE,
                method: "PATCH",
                body: data,
            }),
            transformResponse: (resp) => transformResponseHandler(resp),
            invalidatesTags: ['Project'],
        }),

        deleteProject: builder.mutation({
            query: (data) => ({
                url: apiConfig.DELETE_PROJECT,
                method: "DELETE",
                body: data,
            }),
            transformResponse: (resp) => transformResponseHandler(resp),
            invalidatesTags: ['Project'],
        }),
        getProjectDetails: builder.mutation({
            query: (data) => ({
                url: apiConfig.GET_PROJECT_DETAILS,
                method: "POST",
                body: data,
            }),
        }),
        getProjectStatistics: builder.mutation({
            query: (data) => ({
                url: apiConfig.GET_PROJECT_STATISTICS,
                method: "POST",
                body: data,
            }),
        }),

        getProjectLogs: builder.query({
            query: ({
                page = 1,
                pagesize = 10,
                searchText = "",
                projectId,
                environment = "",
                httpMethod = "",
                ipAddress = "",
                userEmail = "",
                userRole = "",
                responseCode = "",
                errorType = "",
                minResponseTime = "",
                maxResponseTime = "",
                sortBy = "timestamp",
                sortOrder = "desc"
            }) => ({
                url: apiConfig.PROJECT_LOGS_LISTING,
                method: "POST",
                body: {
                    page,
                    pagesize,
                    searchText,
                    projectId,
                    environment,
                    httpMethod,
                    ipAddress,
                    userEmail,
                    userRole,
                    responseCode,
                    errorType,
                    minResponseTime,
                    maxResponseTime,
                    sortBy,
                    sortOrder
                },
            }),
            transformResponse: (resp) => {
                return {
                    logs: resp.data || [],
                    totalCount: resp.total || 0,
                    currentPage: resp.page || 1,
                    perPage: resp.perPage || 10,
                };
            },
            providesTags: ['ProjectLogs'],
        }),

        getProjectLogsUserEmails: builder.query({
            query: ({ projectId, searchText = "" }) => ({
                url: apiConfig.PROJECT_LOGS_USER_EMAILS,
                method: "POST",
                body: {
                    projectId,
                    searchText
                },
            }),
            transformResponse: (resp) => {
                return resp.data || [];
            },
        }),

        getProjectLogsUserRoles: builder.query({
            query: ({ projectId, searchText = "", environment = "" }) => ({
                url: apiConfig.PROJECT_LOGS_USER_ROLES,
                method: "POST",
                body: {
                    projectId,
                    searchText,
                    environment
                },
            }),
            transformResponse: (resp) => {
                return resp.data || [];
            },
        }),

        getProjectEnvironments: builder.query({
            query: ({ projectId }) => ({
                url: apiConfig.PROJECT_ENVIRONMENTS,
                method: "POST",
                body: {
                    projectId
                },
            }),
            transformResponse: (resp) => {
                return resp.data || [];
            },
        }),

        generateApiKey: builder.mutation({
            query: (data) => ({
                url: apiConfig.GENERATE_API_KEY,
                method: "POST",
                body: data,
            }),
            transformResponse: (resp) => transformResponseHandler(resp),
            invalidatesTags: ['Project', 'ApiKeys'],
        }),

        getApiKeyListing: builder.query({
            query: ({
                page = 1,
                pagesize = 10,
                searchText = "",
                status = true,
                projectId,
                environment = ""
            }) => ({
                url: apiConfig.API_KEY_LISTING,
                method: "POST",
                body: {
                    page,
                    pagesize,
                    searchText,
                    status,
                    projectId,
                    environment
                },
            }),
            transformResponse: (resp) => {
                return {
                    apiKeys: resp.data?.projectApiKeys || [],
                    activeApiKeys: resp.data?.activeApiKeys || 0,
                    totalCount: resp.total || 0,
                    currentPage: resp.page || 1,
                    perPage: resp.perPage || 10,
                };
            },
                         providesTags: ['ApiKeys'],
         }),

         getCommonEnvironments: builder.query({
             query: () => ({
                 url: apiConfig.COMMON_ENVIRONMENTS,
                 method: "GET",
             }),
             transformResponse: (resp) => {
                 return resp.data || [];
             },
         }),

         getProjectLogDetail: builder.query({
             query: ({ logId, projectId, environment }) => ({
                 url: apiConfig.PROJECT_LOG_DETAIL,
                 method: "POST",
                 body: {
                     logId,
                     projectId,
                     environment
                 }
             }),
             transformResponse: (resp) => {
                 return resp.data || {};
             },
         }),

         deleteApiKey: builder.mutation({
             query: ({ projectId, apiKeyId }) => ({
                 url: apiConfig.DELETE_API_KEY,
                 method: "POST",
                 body: {
                     projectId,
                     apiKeyId
                 }
             }),
             transformResponse: (resp) => transformResponseHandler(resp),
             invalidatesTags: ['ApiKeys'],
         }),
    }),
});

export const {
    useGetProjectListingQuery,
    useCreateUpdateProjectMutation,
    useToggleProjectFavouriteMutation,
    useDeleteProjectMutation,
    useGetProjectDetailsMutation,
    useGetProjectStatisticsMutation,
    useGetProjectLogsQuery,
    useGetProjectLogsUserEmailsQuery,
    useGetProjectLogsUserRolesQuery,
    useGetProjectEnvironmentsQuery,
    useGenerateApiKeyMutation,
    useGetApiKeyListingQuery,
    useGetCommonEnvironmentsQuery,
    useGetProjectLogDetailQuery,
    useDeleteApiKeyMutation
} = projectApi;

export default projectApi; 