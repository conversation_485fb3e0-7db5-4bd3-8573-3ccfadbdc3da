import { createApi } from "@reduxjs/toolkit/query/react";
import { REDUCER_PATHS } from "../../configs/routeConfig";
import { transformResponseHandler } from "../../utils/transformResponseHandler";
import { customFetchBase } from "../../utils/customFetchBase";

const roleApi = createApi({
  reducerPath: REDUCER_PATHS.ROLE,
  baseQuery: customFetchBase,
  endpoints: (builder) => ({
    getRoleDropdown: builder.query({
      query: () => ({
        url: "/admin/role-dropdown",
        method: "GET",
      }),
    }),
  }),
});

export const { useGetRoleDropdownQuery } = roleApi;
export default roleApi; 