import { createApi } from "@reduxjs/toolkit/query/react";
import { REDUCER_PATHS } from "../../configs/routeConfig";
import { transformResponseHandler } from "../../utils/transformResponseHandler";
import { customFetchBase } from "../../utils/customFetchBase";

const commonApi = createApi({
  reducerPath: REDUCER_PATHS.COMMON,
  baseQuery: customFetchBase,
  endpoints: (builder) => ({
    uploadImage: builder.mutation({
      query: (imageFile) => {
        const formData = new FormData();
        formData.append('file', imageFile);
        
        return {
          url: "/admin/image-file-upload",
          method: "POST",
          body: formData,
        };
      },
      transformResponse: (resp) => transformResponseHandler(resp),
    }),
  }),
});

export const { useUploadImageMutation } = commonApi;
export default commonApi; 