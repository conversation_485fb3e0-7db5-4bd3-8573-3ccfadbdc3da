import { createReducer, createAction } from "@reduxjs/toolkit";

export const loginUserKey = createAction("auth/login");
export const logoutUserKey = createAction("auth/logout");
const initialState = {
  access_token: "",
  refreshToken: "",
  data: {},
};

const userTokenReducer = createReducer(initialState, (builder) => {
  builder
    .addCase(loginUserKey.toString(), (state, action) => {
      state.access_token = action.payload?.access_token || "";
      state.data = action.payload?.data || {};
      state.refreshToken = action.payload?.refreshToken || "";
    })
    .addCase(logoutUserKey.toString(), (state) => {
      state.access_token = "";
      state.data = {};
      state.refreshToken = "";
    });
});

export default userTokenReducer; 