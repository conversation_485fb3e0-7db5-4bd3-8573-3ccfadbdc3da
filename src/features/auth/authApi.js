  import { createApi } from "@reduxjs/toolkit/query/react";
  import apiConfig from "../../configs/apiConfig";
  import { loginUserTokenAction, logoutUserTokenAction } from "./token";
  import { REDUCER_PATHS } from "../../configs/routeConfig";
  import {
    sentOtpTransformResponseHandler,
    transformResponseHandler,
  } from "../../utils/transformResponseHandler";
  import { customFetchBase } from "../../utils/customFetchBase";

  const authApi = createApi({
    reducerPath: REDUCER_PATHS.AUTH,
    baseQuery: customFetchBase,
    endpoints: (builder) => ({
      login: builder.mutation({
        query: (data) => ({
          headers: {
            deviceid: "web",
          },
          url: apiConfig.LOGIN,
          body: data,
          method: "POST",
        }),
        transformResponse: (resp) => transformResponseHandler(resp),
        onQueryStarted: async (_, { dispatch, queryFulfilled }) => {
          const { data } = await queryFulfilled;
          if (data?.extraMeta) {
            dispatch(
              loginUserTokenAction({ data: data.data, ...data.extraMeta })
            );
          }
        },
        // Removed onQueryStarted to avoid double-dispatch and payload issues
      }),
      forgotPassword: builder.mutation({
        query: (data) => ({
          url: "/admin/forgot-password",
          method: "POST",
          body: data,
        }),
        transformResponse: (resp) => transformResponseHandler(resp),
      }),
      resetPassword: builder.mutation({
        query: (data) => ({
          url: "/admin/reset-password",
          method: "POST",
          body: data,
        }),
        transformResponse: (resp) => transformResponseHandler(resp),
      }),
      getProfile: builder.query({
        query: () => ({
          url: "/admin/profile",
          method: "GET",
        }),
        // transformResponse: (resp) => transformResponseHandler(resp),
      }),
          editProfile: builder.mutation({
      query: (data) => ({
        url: "/admin/edit-profile",
        method: "POST",
        body: data,
      }),
      transformResponse: (resp) => transformResponseHandler(resp),
    }),
    changePassword: builder.mutation({
      query: (data) => ({
        url: "/admin/change-password",
        method: "POST",
        body: data,
      }),
      transformResponse: (resp) => transformResponseHandler(resp),
    }),
    }),
  });

  export const { useLoginMutation ,useForgotPasswordMutation, useResetPasswordMutation, useGetProfileQuery, useEditProfileMutation, useChangePasswordMutation} = authApi;
  export default authApi;
