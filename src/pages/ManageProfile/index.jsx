import React, { useState, useEffect, useMemo } from "react";
import { Link } from "react-router-dom";
import PrivateLayout from "../../components/PrivateLayout";
import {
  useGetProfileQuery,
  useEditProfileMutation,
} from "../../features/auth/authApi";
import { useGetRoleDropdownQuery } from "../../features/role/roleApi";
import { useUploadImageMutation } from "../../features/common/commonApi";
import ct from 'countries-and-timezones';
import Select from 'react-select';
import {
  FaCamera,
  FaUser,
  FaEnvelope,
  FaPhone,
  FaGlobe,
  FaClock,
  FaCalendar,
  FaKey,
  FaEdit,
  FaCheck,
  FaTimes,
} from "react-icons/fa";
import { showToast } from "../../utils/toast";
import defaultProfilePic from "../../assets/profile-pic.jpg";

const ManageProfile = () => {
  const { data: profile, isLoading, error, refetch: refetchProfile } = useGetProfileQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });
  const { data: roleDropdown, isLoading: roleLoading } =
    useGetRoleDropdownQuery(undefined, {
      refetchOnMountOrArgChange: true,
    });
  const [editProfile, { isLoading: isUpdating }] = useEditProfileMutation();
  const [uploadImage, { isLoading: isUploadingImage }] = useUploadImageMutation();
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    role: "",
    timeZone: "UTC",
    // language: "English",
    dateFormat: "MM/DD/YYYY",
    timeFormat: "12",
  });
  const [profileImagePath, setProfileImagePath] = useState(null);
  const [imagePreview, setImagePreview] = useState(defaultProfilePic);
  const [isImageUploading, setIsImageUploading] = useState(false);
  const [originalFormData, setOriginalFormData] = useState(null);

  // Generate timezone options from countries-and-timezones package
  const timezoneOptions = useMemo(() => {
    const timezones = ct.getAllTimezones();
    const options = [];

    // Add all timezones alphabetically from the package
    const allTimezoneEntries = Object.entries(timezones)
      .sort(([a], [b]) => a.localeCompare(b));

    allTimezoneEntries.forEach(([tz, timezone]) => {
      const offsetHours = timezone.utcOffset / 60;
      const offsetString = offsetHours >= 0 ? `+${offsetHours}` : `${offsetHours}`;
      const label = tz === 'UTC' ? 'UTC (Coordinated Universal Time)' : 
                   `${tz.replace(/_/g, ' ')} (UTC${offsetString})`;
      options.push({
        value: tz,
        label: label,
        // Add extra properties for better searching
        searchTerms: `${tz} ${tz.replace(/_/g, ' ')} ${timezone.name || ''} UTC${offsetString}`
      });
    });

    return options;
  }, []);

  // Get user's current timezone
  const getUserTimezone = () => {
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch (error) {
      return 'UTC'; // fallback
    }
  };

  // Force fetch profile on component mount
  useEffect(() => {
    if (!profile && !isLoading && !error) {
      refetchProfile();
    }
  }, [profile, isLoading, error, refetchProfile]);

  useEffect(() => {
    if (profile?.data) {
      const initialData = {
        firstName: profile?.data?.firstname || "",
        lastName: profile?.data?.lastname || "",
        email: profile?.data?.emailId || "",
        phone: profile?.data?.mobile || "",
        role: profile?.data?.role?._id || "",
        timeZone: profile?.data?.timeZone || getUserTimezone(),
        dateFormat: profile?.data?.dateFormat || "MM/DD/YYYY",
        timeFormat: profile?.data?.timeFormat || "12",
        status: profile?.data?.status || "active",
      };
      
      setFormData(initialData);
      setOriginalFormData(initialData);
      // Set profile image or use default
      setImagePreview(profile?.data?.photo || defaultProfilePic);
    }
  }, [profile, error]);

    const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    // Restrict firstName and lastName to only letters and spaces
    if (name === "firstName" || name === "lastName") {
      const lettersOnlyRegex = /^[a-zA-Z\s]*$/;
      if (!lettersOnlyRegex.test(value)) {
        return; // Don't update state if invalid characters are entered
      }
    }
    
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  // Handle timezone select change
  const handleTimezoneChange = (selectedOption) => {
    setFormData((prev) => ({
      ...prev,
      timeZone: selectedOption ? selectedOption.value : getUserTimezone(),
    }));
  };

  // Create options for Date Format
  const dateFormatOptions = [
    { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
    { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
    { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' }
  ];

  // Create options for Time Format
  const timeFormatOptions = [
    { value: '12', label: '12 Hour' },
    { value: '24', label: '24 Hour' }
  ];

  // Handle role select change
  const handleRoleChange = (selectedOption) => {
    setFormData((prev) => ({
      ...prev,
      role: selectedOption ? selectedOption.value : "",
    }));
  };

  // Handle date format select change
  const handleDateFormatChange = (selectedOption) => {
    setFormData((prev) => ({
      ...prev,
      dateFormat: selectedOption ? selectedOption.value : "MM/DD/YYYY",
    }));
  };

  // Handle time format select change
  const handleTimeFormatChange = (selectedOption) => {
    setFormData((prev) => ({
      ...prev,
      timeFormat: selectedOption ? selectedOption.value : "12",
    }));
  };

  // Custom styles for react-select
  const customSelectStyles = {
    control: (provided, state) => ({
      ...provided,
      minHeight: '48px',
      border: '1px solid #d1d5db',
      borderRadius: '8px',
      fontSize: '14px',
      fontWeight: '400',
      boxShadow: state.isFocused ? '0 0 0 2px rgba(59, 130, 246, 0.5)' : 'none',
      borderColor: state.isFocused ? '#3b82f6' : '#d1d5db',
      '&:hover': {
        borderColor: state.isFocused ? '#3b82f6' : '#9ca3af'
      },
      transition: 'all 0.2s ease-in-out'
    }),
    option: (provided, state) => ({
      ...provided,
      fontSize: '14px',
      fontWeight: state.data?.isPopular ? '500' : '400',
      backgroundColor: state.isSelected 
        ? '#3b82f6' 
        : state.isFocused 
        ? '#f3f4f6' 
        : 'white',
      color: state.isSelected ? 'white' : '#374151',
      padding: '8px 12px'
    }),
    menu: (provided) => ({
      ...provided,
      zIndex: 9999,
      maxHeight: '300px'
    }),
    menuList: (provided) => ({
      ...provided,
      maxHeight: '280px'
    }),
    placeholder: (provided) => ({
      ...provided,
      color: '#9ca3af',
      fontSize: '14px'
    }),
    singleValue: (provided) => ({
      ...provided,
      color: '#374151',
      fontSize: '14px'
    })
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const allowedTypes = [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/webp",
      ];
      if (!allowedTypes.includes(file.type)) {
        showToast(
          "error",
          "Please select a valid image file (JPEG, PNG, GIF, or WebP)"
        );
        return;
      }

      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        showToast("error", "Image size should be less than 5MB");
        return;
      }

      setIsImageUploading(true);

      try {
        // Show preview immediately
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview(e.target.result);
        };
        reader.readAsDataURL(file);

        // Upload image to server
        const response = await uploadImage(file).unwrap();
        
        if (response?.data?.filePath) {
          setProfileImagePath(response.data.filePath);
        } else {
          throw new Error("Invalid response from server");
        }
      } catch (error) {
        console.error("Error uploading image:", error);        // Reset preview on error
        setImagePreview(profile?.data?.photo || defaultProfilePic);
      } finally {
        setIsImageUploading(false);
      }
    }
  };

  const handleSave = async (e) => {
    e.preventDefault();
    try {
      // Prepare the data to send to the API
      const profileData = {
        firstname: formData.firstName,
        lastname: formData.lastName,
        role: formData.role,
        timeZone: formData.timeZone,
        dateFormat: formData.dateFormat,
        timeFormat: formData.timeFormat,
      };

      // If profile image was uploaded, include the file path
      if (profileImagePath) {
        profileData.photo = profileImagePath;
      }

      await editProfile(profileData).unwrap();
    } catch (error) {
      console.error("Error updating profile:", error);
    }
  };

  const handleDiscardChanges = () => {
    if (profile?.data && originalFormData) {
      setFormData(originalFormData);
      // Reset profile image to original
      setImagePreview(profile?.data?.photo || defaultProfilePic);
      setProfileImagePath(null);
      showToast("info", "Changes discarded");
    }
  };

  // Check if there are any changes in the form
  const hasChanges = () => {
    if (!originalFormData) return false;
    
    // Check if form data has changed
    const formHasChanges = Object.keys(originalFormData).some(
      key => formData[key] !== originalFormData[key]
    );
    
    // Check if image has changed
    const imageHasChanges = profileImagePath !== null;
    
    return formHasChanges || imageHasChanges;
  };

  if (isLoading || (!profile && !error)) {
    return (
      <PrivateLayout>
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="text-center">
            <div className="inline-block w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <div className="mt-2 text-gray-600">Loading profile...</div>
          </div>
        </div>
      </PrivateLayout>
    );
  }

  if (error) {
    return (
      <PrivateLayout>
        <div className="max-w-4xl mx-auto mt-8 p-6">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <h4 className="font-medium">Error loading profile</h4>
            <p>Please try again later.</p>
          </div>
        </div>
      </PrivateLayout>
    );
  }

  return (
    <PrivateLayout>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Combined Profile Form */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
            {/* Cover Background */}
            <div className="h-32 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>

            <form onSubmit={handleSave} className="relative">
              {/* Profile Image Section - Centered */}
              <div className="flex justify-center -mt-16 px-8 pb-8">
                <div className="relative">
                  <div className="w-32 h-32 rounded-full border-4 border-white shadow-lg overflow-hidden bg-white">
                    <img
                      src={imagePreview}
                      alt="Profile"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.src = defaultProfilePic;
                      }}
                    />
                  </div>
                  <label
                    htmlFor="profileImageInput"
                    className="absolute bottom-2 right-2 bg-blue-600 text-white p-3 rounded-full cursor-pointer hover:bg-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    {isImageUploading || isUploadingImage ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    ) : (
                      <FaCamera className="w-4 h-4" />
                    )}
                  </label>
                  <input
                    id="profileImageInput"
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                </div>
              </div>

              {/* Profile Header Info - Centered */}
              <div className="text-center px-8 pb-8 border-b border-gray-200">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  {formData.firstName} {formData.lastName}
                </h1>
                <p className="text-gray-600 mb-4 text-lg">{formData.email}</p>
                <p className="text-gray-500 mb-4">
                  {roleDropdown?.data?.find(
                    (roleItem) => roleItem._id === formData.role
                  )?.role ||
                    profile?.data?.role?.role ||
                    ""}
                </p>
                <div className="flex justify-center items-center space-x-4">
                  <span
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      formData.status
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {formData.status ? (
                      <FaCheck className="w-3 h-3 mr-1" />
                    ) : (
                      <FaTimes className="w-3 h-3 mr-1" />
                    )}
                    {formData.status ? "Active" : "Inactive"}
                  </span>
                  <Link
                    to="/change-password"
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium text-sm"
                  >
                    <FaKey className="w-3 h-3 mr-2" />
                    Change Password
                  </Link>
                </div>
              </div>

              {/* Form Content */}
              <div className="p-8">
                {/* Personal Information Section */}
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <FaUser className="w-5 h-5 mr-2 text-blue-600" />
                    Personal Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* First Name */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        First Name
                      </label>
                      <input
                        type="text"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        placeholder="Enter your first name"
                        pattern="[a-zA-Z\s]*"
                        title="Only letters and spaces are allowed"
                      />
                    </div>

                    {/* Last Name */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Last Name
                      </label>
                      <input
                        type="text"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        placeholder="Enter your last name"
                        pattern="[a-zA-Z\s]*"
                        title="Only letters and spaces are allowed"
                      />
                    </div>

                    {/* Email */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <FaEnvelope className="w-4 h-4 inline mr-1" />
                        Email Address
                      </label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        readOnly
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-600 cursor-not-allowed transition-all duration-200"
                        placeholder="Enter your email"
                      />
                    </div>

                    {/* Phone */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <FaPhone className="w-4 h-4 inline mr-1" />
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        readOnly
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-600 cursor-not-allowed transition-all duration-200"
                        placeholder="Enter your phone number"
                      />
                    </div>

                    {/* Role */}
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Role
                      </label>
                      <Select
                        value={roleDropdown?.data?.find(roleItem => roleItem._id === formData.role) ? 
                          { value: formData.role, label: roleDropdown?.data?.find(roleItem => roleItem._id === formData.role)?.role } : null}
                        onChange={handleRoleChange}
                        options={roleDropdown?.data?.map(roleItem => ({
                          value: roleItem._id,
                          label: roleItem.role
                        })) || []}
                        styles={customSelectStyles}
                        isSearchable={true}
                        isLoading={roleLoading}
                        placeholder={roleLoading ? "Loading roles..." : "Search and select role..."}
                        noOptionsMessage={({ inputValue }) => 
                          inputValue ? `No role found for "${inputValue}"` : "No roles available"
                        }
                        isDisabled={roleLoading}
                      />
                    </div>
                  </div>
                </div>

                {/* Preferences Section */}
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <FaGlobe className="w-5 h-5 mr-2 text-blue-600" />
                    Preferences
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Timezone - Taking full width on medium screens and above */}
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Timezone
                      </label>
                      <Select
                        value={timezoneOptions.find(option => option.value === formData.timeZone)}
                        onChange={handleTimezoneChange}
                        options={timezoneOptions}
                        styles={customSelectStyles}
                        isSearchable={true}
                        placeholder="Search and select timezone..."
                        noOptionsMessage={({ inputValue }) => 
                          inputValue ? `No timezone found for "${inputValue}"` : "No timezones available"
                        }
                        filterOption={(option, inputValue) => {
                          if (!inputValue) return true;
                          const searchValue = inputValue.toLowerCase();
                          return (
                            option.label.toLowerCase().includes(searchValue) ||
                            option.value.toLowerCase().includes(searchValue) ||
                            option.data?.searchTerms?.toLowerCase().includes(searchValue)
                          );
                        }}
                                                 formatOptionLabel={(option) => (
                           <div>
                             <div className="font-normal">
                               {option.label}
                             </div>
                           </div>
                         )}
                      />
                    </div>

                    {/* Time Format */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Time Format
                      </label>
                      <Select
                        value={timeFormatOptions.find(option => option.value === formData.timeFormat)}
                        onChange={handleTimeFormatChange}
                        options={timeFormatOptions}
                        styles={customSelectStyles}
                        isSearchable={true}
                        placeholder="Search and select time format..."
                        noOptionsMessage={({ inputValue }) => 
                          inputValue ? `No time format found for "${inputValue}"` : "No time formats available"
                        }
                      />
                    </div>
                  </div>

                  {/* Second row for Date Format */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                    {/* Date Format */}
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Date Format
                      </label>
                      <Select
                        value={dateFormatOptions.find(option => option.value === formData.dateFormat)}
                        onChange={handleDateFormatChange}
                        options={dateFormatOptions}
                        styles={customSelectStyles}
                        isSearchable={true}
                        placeholder="Search and select date format..."
                        noOptionsMessage={({ inputValue }) => 
                          inputValue ? `No date format found for "${inputValue}"` : "No date formats available"
                        }
                      />
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
                  <button
                    type="submit"
                    disabled={isUpdating}
                    className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 font-medium flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isUpdating ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Updating...
                      </>
                    ) : (
                      <>
                        <FaCheck className="w-4 h-4 mr-2" />
                        Update Profile
                      </>
                    )}
                  </button>
                  <button
                    type="button"
                    onClick={handleDiscardChanges}
                    disabled={!hasChanges()}
                    className={`flex-1 px-6 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 font-medium flex items-center justify-center ${
                      hasChanges()
                        ? "bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500"
                        : "bg-gray-50 text-gray-400 cursor-not-allowed"
                    }`}
                  >
                    <FaTimes className="w-4 h-4 mr-2" />
                    Discard Changes
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
};

export default ManageProfile;
