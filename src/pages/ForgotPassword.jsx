import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { FaShieldAlt, FaEnvelope, FaArrowLeft, FaInfoCircle, FaPaperPlane, FaExclamationTriangle } from 'react-icons/fa';
import { useForgotPasswordMutation } from '../features/auth/authApi';
import { showToast, ToastContainer } from '../utils/toast';
import { cn } from '../utils/cn';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [emailSent, setEmailSent] = useState(false);
  const [forgotPassword, { isLoading, error }] = useForgotPasswordMutation();

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const result = await forgotPassword({ emailId: email }).unwrap();
      setEmailSent(true);
    } catch (err) {
      // showToast('error', err?.data?.message || 'Failed to send reset email. Please try again.');
    }
  };

  if (emailSent) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-900 relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-200"></div>
          <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-sky-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-400"></div>
        </div>

        {/* Floating particles effect */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-white rounded-full opacity-10 animate-pulse"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${3 + Math.random() * 2}s`
              }}
            />
          ))}
        </div>

        {/* Main Content */}
        <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
          <div className="w-full max-w-lg animate-fade-in">
            <div className="backdrop-blur-xl bg-white/10 border border-white/20 rounded-3xl p-10 md:p-12 shadow-2xl text-center">
              <div className="mb-10">
                <div className="inline-flex items-center justify-center w-24 h-24 bg-green-500/20 backdrop-blur-sm border border-green-400/30 rounded-2xl mb-8">
                  <FaPaperPlane className="text-4xl text-green-300" />
                </div>
                <h3 className="text-4xl font-bold text-white mb-6">Check Your Email</h3>
                <p className="text-blue-200 mb-6 leading-relaxed text-lg">
                  We've sent password reset instructions to
                </p>
                <div className="mb-6">
                  <span className="text-white font-medium bg-white/10 px-4 py-2 rounded-lg border border-white/20 text-lg">
                    {email}
                  </span>
                </div>
                <p className="text-blue-300 text-base leading-relaxed">
                  Didn't receive the email? Check your spam folder or try again.
                </p>
              </div>
              
              <div className="space-y-5">
                <button
                  onClick={() => {
                    setEmailSent(false);
                    setEmail('');
                  }}
                  className="w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-200 transform bg-gradient-to-r from-blue-500 to-cyan-500 text-white hover:from-blue-600 hover:to-cyan-600 hover:scale-[1.02] hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent active:scale-[0.98]"
                >
                  Try Different Email
                </button>
                <Link
                  to="/login"
                  className="w-full inline-flex items-center justify-center px-6 py-4 rounded-xl font-medium text-lg transition-all duration-200 transform bg-white/10 border border-white/20 text-blue-200 hover:bg-white/15 hover:text-white hover:scale-[1.02] backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent active:scale-[0.98]"
                >
                  <FaArrowLeft className="mr-3" />
                  Back to Login
                </Link>
              </div>
            </div>
          </div>
        </div>
        <ToastContainer position="top-right" autoClose={3000} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-200"></div>
        <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-sky-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-400"></div>
      </div>

      {/* Floating particles effect */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white rounded-full opacity-10 animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-6xl">
          {/* Logo and Branding */}
          <div className="text-center mb-8 animate-fade-in">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 backdrop-blur-lg rounded-2xl mb-6 shadow-2xl">
              <FaShieldAlt className="text-3xl text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
              Log Management System
            </h1>
            <p className="text-xl text-blue-200">
              Secure password recovery
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Side - Information */}
            <div className="hidden lg:block animate-fade-in">
              <div className="space-y-8">
                <div>
                  <h2 className="text-3xl font-bold text-white mb-6">
                    Reset Your Password
                  </h2>
                  <p className="text-lg text-blue-100 leading-relaxed mb-8">
                    Enter your email address and we'll send you secure instructions to reset your password.
                  </p>
                </div>

                <div className="grid gap-6">
                  {[
                    {
                      icon: '📧',
                      title: 'Email Verification',
                      description: 'We\'ll send a secure reset link to your registered email address.'
                    },
                    {
                      icon: '🔒',
                      title: 'Secure Process',
                      description: 'Our password reset process uses industry-standard security protocols.'
                    },
                    {
                      icon: '⏰',
                      title: 'Quick Recovery',
                      description: 'Reset links are valid for 24 hours and expire automatically for security.'
                    },
                    {
                      icon: '🛡️',
                      title: 'Account Protection',
                      description: 'Your account remains secure throughout the entire reset process.'
                    }
                  ].map((feature, index) => (
                    <div 
                      key={index}
                      className="group flex items-start space-x-4 p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 transition-all duration-300"
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="text-2xl">{feature.icon}</div>
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-1">{feature.title}</h3>
                        <p className="text-blue-200 text-sm">{feature.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Side - Forgot Password Form */}
            <div className="animate-fade-in">
              <div className="backdrop-blur-xl bg-white/10 border border-white/20 rounded-3xl p-8 md:p-10 shadow-2xl">
                <div className="text-center mb-8">
                  <h3 className="text-3xl font-bold text-white mb-2">Forgot Password?</h3>
                  <p className="text-blue-200">
                    No worries! Enter your email and we'll help you reset it.
                  </p>
                </div>

                {/* Error Message */}
                {error && (
                  <div className="bg-red-500/20 border border-red-400/30 rounded-xl p-4 mb-6 backdrop-blur-sm animate-slide-in">
                    <div className="flex items-start">
                      <FaExclamationTriangle className="mr-3 text-red-300 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-red-200 font-medium">Reset Failed</p>
                        <p className="text-red-300 text-sm mt-1">
                          {error?.data?.message || 'Failed to send reset email. Please try again.'}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Forgot Password Form */}
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Email Field */}
                  <div className="space-y-2">
                    <label htmlFor="email" className="block text-sm font-medium text-blue-200">
                      Email Address
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <FaEnvelope className="text-blue-300 transition-colors duration-200" />
                      </div>
                                             <input
                        type="email"
                        id="email"
                        name="email"
                        placeholder="Enter your email address"
                        value={email}
                        onChange={e => setEmail(e.target.value)}
                        required
                        className="w-full pl-12 pr-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-blue-300 backdrop-blur-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent focus:border-blue-400 focus:bg-white/15 hover:bg-white/15 autofill-transparent"
                      />
                    </div>
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    disabled={isLoading}
                    className={cn(
                      "w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-200 transform",
                      "bg-gradient-to-r from-blue-500 to-cyan-500 text-white",
                      "hover:from-blue-600 hover:to-cyan-600 hover:scale-[1.02] hover:shadow-xl",
                      "focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent",
                      "active:scale-[0.98]",
                      "flex items-center justify-center",
                      isLoading && "opacity-50 cursor-not-allowed hover:scale-100"
                    )}
                  >
                    {isLoading ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-3"></div>
                        Sending Instructions...
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <FaPaperPlane className="mr-3" />
                        Send Reset Instructions
                      </div>
                    )}
                  </button>
                </form>

                {/* Back to Login */}
                <div className="text-center mt-6">
                  <Link 
                    to="/login" 
                    className="inline-flex items-center text-blue-300 hover:text-white font-medium transition-colors duration-200"
                  >
                    <FaArrowLeft className="mr-2" />
                    Back to Login
                  </Link>
                </div>

                {/* Info Alert */}
                <div className="mt-8 p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl">
                  <div className="flex items-center mb-4">
                    <FaInfoCircle className="mr-2 text-blue-300" />
                    <h6 className="font-medium text-blue-200">Having trouble?</h6>
                  </div>
                  <p className="text-blue-300 text-sm">
                    If you continue to have issues, please contact our support team for assistance.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ToastContainer position="top-right" autoClose={3000} />
    </div>
  );
};

export default ForgotPassword; 