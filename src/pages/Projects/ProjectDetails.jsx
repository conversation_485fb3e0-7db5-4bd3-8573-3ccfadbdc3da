import React, { useState, useEffect, useCallback } from "react";
import PrivateLayout from "../../components/PrivateLayout";
import CommonDataTable from "../../components/CommonDataTable";
import LogDetailsSlider from "../../components/LogDetailsSlider";
import CommonPagination from "../../components/CommonPagination";
import CommonSelect from "../../components/CommonSelect";
import { FaSpinner, FaSync } from "react-icons/fa";
import { useNavigate, useParams } from "react-router-dom";
import ProjectStatistics from "./ProjectStatistics";
import { cn } from "../../utils/cn";
import { Formik } from "formik";
import CommonModal from "../../components/CommonModal";
import CommonDatePicker from "../../components/CommonDatePicker";
import { apiKeySchema } from "../../validations/apiKeySchema";
import { showToast } from "../../utils/toast";
import { confirmDialog } from "../../utils/alert";
import {
  useGetProjectLogsQuery,
  useGetProjectLogsUserEmailsQuery,
  useGetProjectLogsUserRolesQuery,
  useGetProjectEnvironmentsQuery,
  useGenerateApiKeyMutation,
  useGetApiKeyListingQuery,
  useGetCommonEnvironmentsQuery,
  useGetProjectLogDetailQuery,
  useDeleteApiKeyMutation,
} from "../../features/projects/projectApi";
import {
  ApiPerformanceChart,
  RequestVolumeChart,
  HttpMethodChart,
  PlatformDistributionChart,
  ResponseCodeChart,
  RequestsSummaryTable,
  UserJourneyAnalysis
} from "../../components/ReportsCharts";
import ChartModal from "../../components/ChartModal";


const dummyProject = {
  id: 1,
  name: "E-commerce Platform",
  apis: 25,
  requests: 1200000,
  users: 50000,
  apiSuccessRate: 98.5,
  logEnabled: true,
  type: ["Web", "Mobile"],
  retention: 30,
  retentionUnit: "Days",
  dateRange: "Aug-2024 - Jul-2025",
};

const apiStatusCodes = [
  100, 101, 200, 201, 202, 204, 301, 302, 304, 400, 401, 403, 404, 405, 409,
  415, 429, 500, 501, 502, 503, 504,
];

const getLogsColumns = (onRowClick, handleShowPopover, handleSort, handleViewDetails) => [
  {
    name: "Time Stamp",
    selector: (row) => row.timestamp || row.timeStamp,
    sortable: true,
    sortField: "timestamp",
    cell: (row) => (
      <div
        style={{ cursor: "pointer" }}
        onClick={() => onRowClick(row)}
        className="text-primary"
      >
        {row.timestamp || row.timeStamp || "--"}
      </div>
    ),
  },
  {
    name: "HTTP",
    selector: (row) => row.httpMethod || row.method,
    sortable: true,
    sortField: "httpMethod",
    cell: (row) => {
      const method = row.httpMethod || row.method;
      const colorClass =
        method === "GET"
          ? "bg-blue-100 text-blue-800"
          : method === "POST"
            ? "bg-green-100 text-green-800"
            : method === "PUT"
              ? "bg-yellow-100 text-yellow-800"
              : "bg-red-100 text-red-800";
      return (
        <span
          className={cn(
            "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium cursor-pointer",
            colorClass
          )}
          onClick={() => onRowClick(row)}
        >
          {method || "--"}
        </span>
      );
    },
  },
  {
    name: "End Point",
    selector: (row) => row.endPoint || row.endpoint,
    sortable: true,
    sortField: "endpoint",
    cell: (row) => (
      <div
        style={{ cursor: "pointer" }}
        onClick={() => onRowClick(row)}
        className="text-primary"
      >
        {row.endPoint || row.endpoint || "--"}
      </div>
    ),
  },
  {
    name: "Query Parameter",
    selector: (row) => row.queryParameter,
    cell: (row) => {
      const hasQueryParams = row.queryParameter && Object.keys(row.queryParameter).length > 0;
      return hasQueryParams ? (
        <button
          className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200 transition-colors"
          onClick={(e) => {
            e.stopPropagation();
            // Handle popover for query parameters
            handleShowPopover('queryParameter', row.queryParameter, e.currentTarget);
          }}
        >
          View
        </button>
      ) : (
        <span className="text-gray-400 text-xs">--</span>
      );
    },
  },
  {
    name: "Request Body",
    selector: (row) => row.requestBody,
    cell: (row) => {
      const hasRequestBody = row.requestBody && Object.keys(row.requestBody).length > 0;
      return hasRequestBody ? (
        <button
          className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded hover:bg-green-200 transition-colors"
          onClick={(e) => {
            e.stopPropagation();
            // Handle popover for request body
            handleShowPopover('requestBody', row.requestBody, e.currentTarget);
          }}
        >
          View
        </button>
      ) : (
        <span className="text-gray-400 text-xs">--</span>
      );
    },
  },
  {
    name: "IP Address",
    selector: (row) => row.ipAddress || row.ip,
    sortable: false,
    cell: (row) => (
      <div
        style={{ cursor: "pointer" }}
        onClick={() => onRowClick(row)}
        className="text-primary"
      >
        {row.ipAddress || row.ip || "--"}
      </div>
    ),
  },
  {
    name: "User Email",
    selector: (row) => row.userEmail || row.email,
    sortable: true,
    sortField: "userEmail",
    cell: (row) => (
      <div
        style={{ cursor: "pointer" }}
        onClick={() => onRowClick(row)}
        className="text-primary"
      >
        {row.userEmail || row.email || "--"}
      </div>
    ),
  },
  {
    name: "User Role",
    selector: (row) => row.userRole || row.role,
    sortable: true,
    sortField: "userRole",
    cell: (row) => {
      const role = row.userRole || row.role;
      const colorClass =
        role === "Super Admin"
          ? "bg-blue-100 text-blue-800"
          : role === "Admin"
            ? "bg-green-100 text-green-800"
            : "bg-gray-100 text-gray-800";
      return (
        <span
          className={cn(
            "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium cursor-pointer",
            colorClass
          )}
          onClick={() => onRowClick(row)}
        >
          {role || "--"}
        </span>
      );
    },
  },
  {
    name: "Res Time",
    selector: (row) => row.responseTime || row.resTime,
    sortable: true,
    sortField: "responseTime",
    cell: (row) => (
      <div
        style={{ cursor: "pointer" }}
        onClick={() => onRowClick(row)}
        className="text-primary"
      >
        {row.responseTime || row.resTime || "--"}
      </div>
    ),
  },
  {
    name: "Res Code",
    selector: (row) => row.responseCode || row.resCode,
    sortable: true,
    sortField: "responseCode",
    cell: (row) => {
      const code = row.responseCode || row.resCode;
      const colorClass =
        code >= 200 && code < 300
          ? "bg-green-100 text-green-800"
          : code >= 400
            ? "bg-red-100 text-red-800"
            : "bg-yellow-100 text-yellow-800";
      return (
        <span
          className={cn(
            "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium cursor-pointer",
            colorClass
          )}
          onClick={() => onRowClick(row)}
        >
          {code || "--"}
        </span>
      );
    },
  },
  {
    name: "Actions",
    selector: () => null,
    sortable: false,
    width: "80px",
    cell: (row) => (
      <div className="flex items-center justify-center">
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleViewDetails(row._id);
          }}
          className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
          title="View Details"
        >
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
            />
          </svg>
        </button>
      </div>
    ),
  },
];
const logsData = [
  {
    timestamp: "04-07-2025 3:08:15",
    method: "GET",
    endpoint: "/api/user-manage",
    route: "https://...",
    query: "",
    body: "",
    ip: "***********",
    email: "chandrakanta@...",
    role: "Super Admin",
    resTime: "127 ms",
    resCode: 304,
    errorType: "",
    errorMsg: "",
    errorFile: "",
    queryParameter: {
      page: "1",
      pageSize: "10",
      searchText: "user"
    },
    requestBody: null,
  },
  {
    timestamp: "04-07-2025 3:08:15",
    method: "POST",
    endpoint: "/api/projects",
    route: "--",
    query: '{"projectName": ...}',
    body: "{...}",
    ip: "***********",
    email: "",
    role: "Super Admin",
    resTime: "210 ms",
    resCode: 500,
    errorType: "DatabaseError",
    errorMsg: "Failed to connect...",
    errorFile: "db/connection.js",
    queryParameter: null,
    requestBody: {
      projectName: "New Project",
      projectType: ["Web", "Mobile"],
      logEnabled: true,
      logRetention: 30
    },
  },
  // ... more dummy rows ...
];

const invitedUsers = [
  {
    name: "Sarah Chen",
    email: "<EMAIL>",
    role: "Super Admin",
    invited: "2024-05-01",
    status: "Accepted",
    last: "2024-06-10",
  },
  {
    name: "Michael Rodriguez",
    email: "<EMAIL>",
    role: "Admin",
    invited: "2024-05-10",
    status: "Accepted",
    last: "2024-06-11",
  },
  {
    name: "Emily Watson",
    email: "<EMAIL>",
    role: "Admin",
    invited: "2024-05-15",
    status: "Pending",
    last: "--",
  },
  {
    name: "James Thompson",
    email: "<EMAIL>",
    role: "User",
    invited: "2024-05-20",
    status: "Expired",
    last: "--",
  },
];



const invitedUsersColumns = [
  { name: "User", selector: (row) => row.name, sortable: true },
  { name: "Email", selector: (row) => row.email },
  {
    name: "Role",
    selector: (row) => row.role,
    cell: (row) => {
      const colorClass =
        row.role === "Super Admin"
          ? "bg-blue-100 text-blue-800"
          : row.role === "Admin"
            ? "bg-green-100 text-green-800"
            : "bg-gray-100 text-gray-800";
      return (
        <span
          className={cn(
            "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
            colorClass
          )}
        >
          {row.role}
        </span>
      );
    },
  },
  { name: "Invited Date", selector: (row) => row.invited },
  {
    name: "Status",
    selector: (row) => row.status,
    cell: (row) => {
      const colorClass =
        row.status === "Accepted"
          ? "bg-green-100 text-green-800"
          : row.status === "Pending"
            ? "bg-yellow-100 text-yellow-800"
            : "bg-red-100 text-red-800";
      return (
        <span
          className={cn(
            "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
            colorClass
          )}
        >
          {row.status}
        </span>
      );
    },
  },
  { name: "Last Activity", selector: (row) => row.last },
  {
    name: "Actions",
    cell: (row) => (
      <button className="px-3 py-1 text-sm text-blue-600 border border-blue-600 rounded hover:bg-blue-50 transition-colors">
        View
      </button>
    ),
  },
];

const getApiKeysColumns = (onDelete) => [
  { 
    name: "Key Name", 
    selector: (row) => row.name, 
    sortable: true,
    cell: (row) => (
      <div className="font-medium text-gray-900">
        {row.name}
      </div>
    )
  },
  { 
    name: "API Key", 
    selector: (row) => row.apiKey,
    cell: (row) => (
      <div className="font-mono text-sm text-gray-600">
        {row.apiKey ? `${row.apiKey.substring(0, 15)}...` : '--'}
      </div>
    )
  },
  {
    name: "Status",
    selector: (row) => row.status,
    cell: (row) => {
      const colorClass = row.status
        ? "bg-green-100 text-green-800"
        : "bg-red-100 text-red-800";
      return (
        <span
          className={cn(
            "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",
            colorClass
          )}
        >
          {row.status ? "Active" : "Inactive"}
        </span>
      );
    },
  },
  { 
    name: "Created", 
    selector: (row) => row.createdAt,
    cell: (row) => (
      <div className="text-sm text-gray-600">
        {row.createdAt ? new Date(row.createdAt).toLocaleDateString() : '--'}
      </div>
    )
  },
  { 
    name: "Expiry Date", 
    selector: (row) => row.expiryDate,
    cell: (row) => (
      <div className="text-sm text-gray-600">
        {row.expiryDate ? new Date(row.expiryDate).toLocaleDateString() : '--'}
      </div>
    )
  },
  { 
    name: "Last Used", 
    selector: (row) => row.lastUsed,
    cell: (row) => (
      <div className="text-sm text-gray-600">
        {row.lastUsed ? new Date(row.lastUsed).toLocaleDateString() : '--'}
      </div>
    )
  },
  {
    name: "Actions",
    cell: (row) => (
      <div className="flex justify-center">
        <button 
          onClick={() => onDelete(row._id || row.id, row.name)}
          className="px-3 py-1 text-xs text-white bg-red-600 rounded hover:bg-red-700 transition-colors flex items-center"
          title="Delete API Key"
        >
          <svg
            className="w-3 h-3 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            />
          </svg>
          Delete
        </button>
      </div>
    ),
  },
];

const ProjectDetails = () => {
  const [tab, setTab] = useState("reports");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [selectedLog, setSelectedLog] = useState(null);
  const [showLogDetails, setShowLogDetails] = useState(false);
  const [selectedEnvironment, setSelectedEnvironment] = useState(null);
  const [popoverData, setPopoverData] = useState({
    show: false,
    type: '', // 'queryParameter' or 'requestBody'
    data: null,
    position: { top: 0, left: 0 }
  });
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [showLogDetailsSlider, setShowLogDetailsSlider] = useState(false);
  const [selectedLogId, setSelectedLogId] = useState(null);
  const [apiKeyCurrentPage, setApiKeyCurrentPage] = useState(1);
  const [apiKeyPageSize, setApiKeyPageSize] = useState(10);
  const [apiKeySearchText, setApiKeySearchText] = useState("");
  const [logsFilter, setLogsFilter] = useState({
    search: "",
    httpMethod: "",
    userEmail: "",
    ipAddress: "",
    userRole: "",
    responseCode: "",
    errorType: "",
    minResponseTime: "",
    maxResponseTime: "",
    sortBy: "createdAt",
    sortOrder: "desc",
  });
  const [appliedLogsFilter, setAppliedLogsFilter] = useState({
    search: "",
    httpMethod: "",
    userEmail: "",
    ipAddress: "",
    userRole: "",
    responseCode: "",
    errorType: "",
    minResponseTime: "",
    maxResponseTime: "",
    sortBy: "createdAt",
    sortOrder: "desc",
  });
  const [showChartModal, setShowChartModal] = useState(false);
  const [selectedChartType, setSelectedChartType] = useState('');
  const [selectedChartTitle, setSelectedChartTitle] = useState('');


  const navigate = useNavigate();
  const { id: projectId } = useParams();

  // API query parameters for logs
  const logsQueryParams = {
    page: currentPage,
    pagesize: pageSize,
    projectId,
    environment: selectedEnvironment?.value || "",
    searchText: appliedLogsFilter.search,
    httpMethod: appliedLogsFilter.httpMethod,
    userEmail: appliedLogsFilter.userEmail,
    ipAddress: appliedLogsFilter.ipAddress,
    userRole: appliedLogsFilter.userRole,
    responseCode: appliedLogsFilter.responseCode,
    errorType: appliedLogsFilter.errorType,
    minResponseTime: appliedLogsFilter.minResponseTime,
    maxResponseTime: appliedLogsFilter.maxResponseTime,
    sortBy: appliedLogsFilter.sortBy,
    sortOrder: appliedLogsFilter.sortOrder,
  };

  // API hooks
  const {
    data: logsData,
    isLoading: logsLoading,
    isError: logsError,
    refetch: refetchLogs,
  } = useGetProjectLogsQuery(logsQueryParams, {
    skip: !projectId || tab !== "logs",
  });

  // Get user emails and roles for dropdowns
  const { data: userEmails = [], isLoading: emailsLoading } =
    useGetProjectLogsUserEmailsQuery(
      { projectId, searchText: "" },
      { skip: !projectId || tab !== "logs" }
    );

  const { data: userRoles = [], isLoading: rolesLoading } =
    useGetProjectLogsUserRolesQuery(
      { projectId, searchText: "", environment: selectedEnvironment?.value || "" },
      { skip: !projectId || tab !== "logs" }
    );

  // Get project environments
  const { data: environments = [], isLoading: environmentsLoading } =
    useGetProjectEnvironmentsQuery(
      { projectId },
      { skip: !projectId }
    );

  // Generate API key mutation
  const [generateApiKey, { isLoading: isGeneratingApiKey }] = useGenerateApiKeyMutation();

  // API key listing query
  const {
    data: apiKeyData,
    isLoading: apiKeysLoading,
    isError: apiKeysError,
    refetch: refetchApiKeys,
  } = useGetApiKeyListingQuery({
    page: apiKeyCurrentPage,
    pagesize: apiKeyPageSize,
    searchText: apiKeySearchText,
    status: true,
    projectId,
    environment: selectedEnvironment?.value || "",
  }, {
    skip: !projectId || tab !== "apikeys",
  });

  // Get common environments for API key generation
  const { data: commonEnvironments = [], isLoading: commonEnvironmentsLoading } =
    useGetCommonEnvironmentsQuery();

  // Delete API key mutation
  const [deleteApiKey, { isLoading: isDeletingApiKey }] = useDeleteApiKeyMutation();

  // Get log detail when modal is open and logId is selected
  const { data: logDetail, isLoading: logDetailLoading, error: logDetailError } =
    useGetProjectLogDetailQuery(
      {
        logId: selectedLogId,
        projectId,
        environment: selectedEnvironment?.value
      },
      {
        skip: !selectedLogId || !projectId || !selectedEnvironment?.value || !showLogDetailsSlider
      }
    );

  // Set default environment when environments are loaded
  useEffect(() => {
    if (environments?.environments?.length > 0 && !selectedEnvironment) {
      const defaultEnv = { value: environments?.environments[0], label: environments?.environments[0] };
      setSelectedEnvironment(defaultEnv);
    }
  }, [environments?.environments, selectedEnvironment]);

  // Extract logs data
  const logs = logsData?.logs || [];
  const totalPages = Math.ceil((logsData?.totalCount || 0) / pageSize);

  // Extract API key data
  const apiKeys = apiKeyData?.apiKeys || [];
  const apiKeyTotalPages = Math.ceil((apiKeyData?.totalCount || 0) / apiKeyPageSize);
  const activeApiKeysCount = apiKeyData?.activeApiKeys || 0;

  // Reset page when applied filters or page size change
  useEffect(() => {
    setCurrentPage(1);
  }, [appliedLogsFilter, pageSize]);

  // Reset API key page when search changes
  useEffect(() => {
    setApiKeyCurrentPage(1);
  }, [apiKeySearchText, apiKeyPageSize, selectedEnvironment]);

  // Handle escape key to close popover
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape' && popoverData.show) {
        handleHidePopover();
      }
    };

    if (popoverData.show) {
      document.addEventListener('keydown', handleEscKey);
      return () => document.removeEventListener('keydown', handleEscKey);
    }
  }, [popoverData.show]);

  // Handle chart click
  const handleChartClick = (chartType, chartTitle) => {
    setSelectedChartType(chartType);
    setSelectedChartTitle(chartTitle);
    setShowChartModal(true);
  };

  // Handle log row click
  const handleLogRowClick = (log) => {
    setSelectedLog(log);
    setShowLogDetails(true);
  };

  // Handle filter reset
  const handleFilterReset = () => {
    const resetFilter = {
      search: "",
      httpMethod: "",
      userEmail: "",
      ipAddress: "",
      userRole: "",
      responseCode: "",
      errorType: "",
      minResponseTime: "",
      maxResponseTime: "",
      sortBy: "createdAt",
      sortOrder: "desc",
    };
    setLogsFilter(resetFilter);
    setAppliedLogsFilter(resetFilter);
    setCurrentPage(1);
  };

  // Handle filter apply
  const handleFilterApply = () => {
    setAppliedLogsFilter({ ...logsFilter });
    setCurrentPage(1);
  };

  // Handle refresh logs
  const handleRefreshLogs = () => {
    refetchLogs();
  };

  // Handle sort
  const handleSort = (column, sortDirection) => {
    const newFilter = {
      ...appliedLogsFilter,
      sortBy: column?.sortField || column?.name?.toLowerCase()?.replace(/\s+/g, ''),
      sortOrder: sortDirection === 'asc' ? 'asc' : 'desc'
    };
    setAppliedLogsFilter(newFilter);
    setLogsFilter(newFilter);
  };

  // Handle popover show
  const handleShowPopover = (type, data, targetElement) => {
    const rect = targetElement.getBoundingClientRect();
    setPopoverData({
      show: true,
      type: type,
      data: data,
      position: {
        top: rect.bottom + window.scrollY + 5,
        left: rect.left + window.scrollX
      }
    });
  };

  // Handle popover hide
  const handleHidePopover = () => {
    setPopoverData({
      show: false,
      type: '',
      data: null,
      position: { top: 0, left: 0 }
    });
  };

  // Handle viewing log details
  const handleViewLogDetail = (logId) => {
    setSelectedLogId(logId);
    setShowLogDetailsSlider(true);
  };

  // Handle closing log detail slider
  const handleCloseLogDetail = () => {
    setShowLogDetailsSlider(false);
    setSelectedLogId(null);
  };

  // Handle API key deletion
  const handleDeleteApiKey = async (apiKeyId, keyName) => {
    try {
      const confirmed = await confirmDialog({
        title: "Delete API Key",
        text: `Are you sure you want to delete the API key "${keyName}"? This action cannot be undone.`,
        icon: "warning",
        confirmButtonText: "Delete",
        confirmButtonColor: "#dc3545",
      });

      if (confirmed) {
        await deleteApiKey({
          projectId,
          apiKeyId
        }).unwrap();
        refetchApiKeys();
      }
    } catch (error) {
      console.error('Error deleting API key:', error);
      showToast("error", error?.data?.message || "Failed to delete API key");
    }
  };

  // Handle API key generation
  const handleGenerateApiKey = async (values, { setSubmitting, resetForm }) => {
    try {
      const payload = {
        projectId,
        keyName: values.keyName,
        environment: values.environment,
        expiryDate: values.expiryDate.toISOString(),
        logRetention: `${values.logRetentionValue} ${values.logRetentionUnit}`
      };

            await generateApiKey(payload).unwrap();
      
      showToast("success", "API key generated successfully!");
      setShowApiKeyModal(false);
      resetForm();
      // Refresh API keys list
      refetchApiKeys();
    } catch (error) {
      console.error('Error generating API key:', error);
      showToast("error", error?.data?.message || "Failed to generate API key");
    } finally {
      setSubmitting(false);
    }
  };

  // Format options for CommonSelect
  const userEmailOptions = userEmails.map((email) => ({
    value: email,
    label: email,
  }));

  const userRoleOptions = userRoles.map((role) => ({
    value: role,
    label: role,
  }));

  const responseCodeOptions = apiStatusCodes.map((code) => ({
    value: code.toString(),
    label: code.toString(),
  }));

  const environmentOptions = environments?.environments?.map((env) => ({
    value: env,
    label: env,
  }));

  return (
    <PrivateLayout>
      <style>
        {`
          .card:hover {
            transform: none !important;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
          }
          .card-body:hover {
            background-color: transparent !important;
          }
        `}
      </style>
      {/* Top Section */}
      <ProjectStatistics
        selectedEnvironment={selectedEnvironment}
        onEnvironmentChange={(selectedOption) => {
          setSelectedEnvironment(selectedOption);
          setCurrentPage(1); // Reset to first page when environment changes
        }}
        environmentOptions={environmentOptions}
        environmentsLoading={environmentsLoading}
        environments={environments}
      />

      {/* Tabs Section */}
      <div className="mb-6">
        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setTab("reports")}
              className={cn(
                "py-2 px-1 border-b-2 font-medium text-sm transition-colors",
                tab === "reports"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              Reports
            </button>
            <button
              onClick={() => setTab("logs")}
              className={cn(
                "py-2 px-1 border-b-2 font-medium text-sm transition-colors",
                tab === "logs"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              Logs
            </button>
            <button
              onClick={() => setTab("users")}
              className={cn(
                "py-2 px-1 border-b-2 font-medium text-sm transition-colors",
                tab === "users"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              Invited Users
            </button>
            <button
              onClick={() => setTab("apikeys")}
              className={cn(
                "py-2 px-1 border-b-2 font-medium text-sm transition-colors",
                tab === "apikeys"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              API Keys
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {tab === "reports" && (
            <div className="grid grid-cols-1 md:grid-cols-[repeat(auto-fit,minmax(400px,1fr))] gap-6 w-full mb-6">
              <div className="bg-white rounded-xl shadow-sm p-5 border border-gray-200 transition-shadow hover:shadow-md cursor-pointer" onClick={() => handleChartClick('apiPerformance', 'API end point performance report')}>
                <div className="text-sm font-semibold text-gray-800 mb-4 leading-relaxed">
                  API end point performance report
                </div>
                <div className="relative mb-3">
                  <ApiPerformanceChart onClick={() => handleChartClick('apiPerformance', 'API end point performance report')} />
                </div>
                <div className="text-xs text-gray-500 leading-relaxed mt-2">
                  Performance metrics for each API endpoint, including response
                  times and error rates.
                </div>
              </div>
              <div className="bg-white rounded-xl shadow-sm p-5 border border-gray-200 transition-shadow hover:shadow-md cursor-pointer" onClick={() => handleChartClick('requestVolume', 'Request Volume by Time')}>
                <div className="text-sm font-semibold text-gray-800 mb-4 leading-relaxed">Request Volume by Time</div>
                <div className="relative mb-3">
                  <RequestVolumeChart onClick={() => handleChartClick('requestVolume', 'Request Volume by Time')} />
                </div>
                <div className="text-xs text-gray-500 leading-relaxed mt-2">
                  Visualization of request volume over time to identify peak
                  usage periods.
                </div>
              </div>
              <div className="bg-white rounded-xl shadow-sm p-5 border border-gray-200 transition-shadow hover:shadow-md cursor-pointer" onClick={() => handleChartClick('httpMethod', 'HTTP Method Distribution')}>
                <div className="text-sm font-semibold text-gray-800 mb-4 leading-relaxed">HTTP Method Distribution</div>
                <div className="relative mb-3">
                  <HttpMethodChart onClick={() => handleChartClick('httpMethod', 'HTTP Method Distribution')} />
                </div>
                <div className="text-xs text-gray-500 leading-relaxed mt-2">
                  Distribution of HTTP methods (GET, POST, etc.) used across all
                  API requests.
                </div>
              </div>
              <div className="bg-white rounded-xl shadow-sm p-5 border border-gray-200 transition-shadow hover:shadow-md cursor-pointer" onClick={() => handleChartClick('platformDistribution', 'Platform Request Distribution')}>
                <div className="text-sm font-semibold text-gray-800 mb-4 leading-relaxed">
                  Platform Request Distribution
                </div>
                <div className="relative mb-3">
                  <PlatformDistributionChart onClick={() => handleChartClick('platformDistribution', 'Platform Request Distribution')} />
                </div>
                <div className="text-xs text-gray-500 leading-relaxed mt-2">
                  Breakdown of requests by mobile and web platforms, showing
                  usage patterns and trends.
                </div>
              </div>
              <div className="bg-white rounded-xl shadow-sm p-5 border border-gray-200 transition-shadow hover:shadow-md cursor-pointer" onClick={() => handleChartClick('responseCode', 'Response code analysis')}>
                <div className="text-sm font-semibold text-gray-800 mb-4 leading-relaxed">Response code analysis</div>
                <div className="relative mb-3">
                  <ResponseCodeChart onClick={() => handleChartClick('responseCode', 'Response code analysis')} />
                </div>
                <div className="text-xs text-gray-500 leading-relaxed mt-2">
                  Analysis of response codes (200, 404, 500, etc.) to monitor
                  API health.
                </div>
              </div>
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden col-span-1 md:col-span-2">
                <RequestsSummaryTable onClick={() => handleChartClick('requestsSummary', 'Requests Summary by IP Address / Email Address / Role')} />
              </div>
              <div className="bg-white rounded-xl shadow-sm p-5 border border-gray-200 transition-shadow hover:shadow-md cursor-pointer flex items-center justify-center min-h-[120px]" onClick={() => handleChartClick('userJourney', 'User journey analysis')}>
                <UserJourneyAnalysis onClick={() => handleChartClick('userJourney', 'User journey analysis')} />
              </div>
            </div>
          )}

          {tab === "logs" && (
            <div>
              {/* Filter Card */}
              <div className="bg-white rounded-lg shadow-sm mb-6">
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-3 mb-4">
                    <div>
                      <input
                        type="text"
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Search by endpoint, IP, email..."
                        value={logsFilter.search}
                        onChange={(e) =>
                          setLogsFilter((f) => ({
                            ...f,
                            search: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <select
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        value={logsFilter.httpMethod}
                        onChange={(e) =>
                          setLogsFilter((f) => ({
                            ...f,
                            httpMethod: e.target.value,
                          }))
                        }
                      >
                        <option value="">All Methods</option>
                        <option value="GET">GET</option>
                        <option value="POST">POST</option>
                        <option value="PUT">PUT</option>
                        <option value="DELETE">DELETE</option>
                      </select>
                    </div>
                    <div>
                      <CommonSelect
                        placeholder="User Email"
                        value={
                          userEmailOptions.find(
                            (option) => option.value === logsFilter.userEmail
                          ) || null
                        }
                        onChange={(selectedOption) =>
                          setLogsFilter((f) => ({
                            ...f,
                            userEmail: selectedOption?.value || "",
                          }))
                        }
                        options={userEmailOptions}
                        isLoading={emailsLoading}
                      />
                    </div>
                    <div>
                      <input
                        type="text"
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="IP Address"
                        value={logsFilter.ipAddress}
                        onChange={(e) =>
                          setLogsFilter((f) => ({
                            ...f,
                            ipAddress: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <CommonSelect
                        placeholder="User Role"
                        value={
                          userRoleOptions.find(
                            (option) => option.value === logsFilter.userRole
                          ) || null
                        }
                        onChange={(selectedOption) =>
                          setLogsFilter((f) => ({
                            ...f,
                            userRole: selectedOption?.value || "",
                          }))
                        }
                        options={userRoleOptions}
                        isLoading={rolesLoading}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-5 gap-3 mb-4">
                    <div>
                      <CommonSelect
                        placeholder="Response Code"
                        value={
                          responseCodeOptions.find(
                            (option) => option.value === logsFilter.responseCode
                          ) || null
                        }
                        onChange={(selectedOption) =>
                          setLogsFilter((f) => ({
                            ...f,
                            responseCode: selectedOption?.value || "",
                          }))
                        }
                        options={responseCodeOptions}
                      />
                    </div>
                    <div>
                      <input
                        type="text"
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Error Type"
                        value={logsFilter.errorType}
                        onChange={(e) =>
                          setLogsFilter((f) => ({
                            ...f,
                            errorType: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <input
                        type="text"
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Min Response Time"
                        value={logsFilter.minResponseTime}
                        onChange={(e) =>
                          setLogsFilter((f) => ({
                            ...f,
                            minResponseTime: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <input
                        type="text"
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Max Response Time"
                        value={logsFilter.maxResponseTime}
                        onChange={(e) =>
                          setLogsFilter((f) => ({
                            ...f,
                            maxResponseTime: e.target.value,
                          }))
                        }
                      />
                    </div>
                    <div>
                      <select
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        value={`${logsFilter.sortBy}-${logsFilter.sortOrder}`}
                        onChange={(e) => {
                          const [sortBy, sortOrder] = e.target.value.split("-");
                          setLogsFilter((f) => ({ ...f, sortBy, sortOrder }));
                        }}
                      >
                        <option value="createdAt-desc">Latest First</option>
                        <option value="createdAt-asc">Oldest First</option>
                        <option value="responseTime-desc">Slowest First</option>
                        <option value="responseTime-asc">Fastest First</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <button
                      className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      onClick={handleFilterApply}
                    >
                      Filter
                    </button>
                    <button
                      className="px-4 py-2 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                      onClick={handleFilterReset}
                    >
                      Reset
                    </button>
                    <button
                      className="px-4 py-2 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                      onClick={handleRefreshLogs}
                      disabled={logsLoading}
                    >
                      {logsLoading ? (
                        <div className="flex items-center">
                          <FaSpinner className="animate-spin mr-1" />
                          Refreshing...
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <FaSync className="mr-1" />
                          Refresh
                        </div>
                      )}
                    </button>
                  </div>
                </div>
              </div>

              {/* Data Table Card */}
              <div className="card shadow-sm" style={{ cursor: "default" }}>
                <div className="card-body p-0">
                  {logsLoading ? (
                    <div className="flex justify-center items-center py-5">
                      <FaSpinner className="animate-spin mr-2" />
                      <span className="ml-2">Loading logs...</span>
                    </div>
                  ) : logsError ? (
                    <div className="text-center py-5">
                      <h6 className="text-lg font-semibold text-gray-900 mb-2">Error loading logs</h6>
                      <p className="text-gray-600 mb-4">
                        Something went wrong while fetching logs.
                      </p>
                      <button
                        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                        onClick={() => refetchLogs()}
                      >
                        Try Again
                      </button>
                    </div>
                  ) : logs.length === 0 ? (
                    <div className="text-center py-5">
                      <h6 className="text-lg font-semibold text-gray-900 mb-2">No logs found</h6>
                      <p className="text-gray-600">
                        No logs match your current filters.
                      </p>
                    </div>
                  ) : (
                    <>
                      <div
                        className="table-responsive"
                        style={{
                          maxHeight: pageSize > 25 ? "600px" : "auto",
                          overflowY: pageSize > 25 ? "auto" : "visible",
                          overflowX: "auto",
                        }}
                      >
                        <CommonDataTable
                          columns={getLogsColumns(handleLogRowClick, handleShowPopover, handleSort, handleViewLogDetail)}
                          data={logs}
                          pagination={false}
                          highlightOnHover={false}
                          sortServer={true}
                          onSort={handleSort}
                          defaultSortFieldId={appliedLogsFilter.sortBy}
                          defaultSortAsc={appliedLogsFilter.sortOrder === 'asc'}
                        />
                      </div>

                      {/* Pagination Section */}
                      <div className="flex justify-between items-center p-3 border-t border-gray-200">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-2">
                            <span className="text-gray-600 text-sm">Rows per page:</span>
                            <select
                              className="px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              style={{ width: "auto" }}
                              value={pageSize}
                              onChange={(e) =>
                                setPageSize(parseInt(e.target.value))
                              }
                            >
                              <option value={5}>5</option>
                              <option value={10}>10</option>
                              <option value={25}>25</option>
                              <option value={50}>50</option>
                              <option value={100}>100</option>
                            </select>
                          </div>
                          {console.log(logsData)}
                          {/* Records Info */}
                          {logsData?.totalCount > 0 && (
                            <div className="text-sm text-gray-600">
                              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, logsData?.totalCount)} out of {logsData?.totalCount} records
                            </div>
                          )}
                        </div>

                        {totalPages > 1 && (
                          <CommonPagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            onPageChange={setCurrentPage}
                          />
                        )}
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}

          {tab === "users" && (
            <div>
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex flex-wrap gap-2 mb-6">
                  <input
                    type="text"
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    style={{ maxWidth: 180 }}
                    placeholder="Enter name"
                  />
                  <input
                    type="email"
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    style={{ maxWidth: 220 }}
                    placeholder="Enter email"
                  />
                  <select
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    style={{ maxWidth: 160 }}
                  >
                    <option>Select role</option>
                  </select>
                  <button className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors">
                    Send Invitation
                  </button>
                  <button className="px-4 py-2 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50 transition-colors">
                    Bulk Invite
                  </button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-gray-900">0</div>
                    <div className="text-sm text-gray-500">Total Invited</div>
                  </div>
                  <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-gray-900">0</div>
                    <div className="text-sm text-gray-500">Accepted</div>
                  </div>
                  <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-gray-900">0</div>
                    <div className="text-sm text-gray-500">Pending</div>
                  </div>
                  <div className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                    <div className="text-2xl font-bold text-gray-900">0</div>
                    <div className="text-sm text-gray-500">Expired</div>
                  </div>
                </div>
                <CommonDataTable
                  columns={invitedUsersColumns}
                  data={invitedUsers}
                />
              </div>
            </div>
          )}

                    {tab === "apikeys" && (
            <div>
              {/* API Keys Header with Search and Stats */}
              <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    API Keys
                  </h3>
                  <button 
                    className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                    onClick={() => setShowApiKeyModal(true)}
                  >
                    + Generate New Key
                  </button>
                </div>

                {/* Search and Stats */}
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Search API keys..."
                        value={apiKeySearchText}
                        onChange={(e) => setApiKeySearchText(e.target.value)}
                        className="w-64 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    <span className="font-medium text-green-600">{activeApiKeysCount}</span> active API keys
                  </div>
                </div>
              </div>

              {/* API Keys Table */}
              <div className="bg-white rounded-lg shadow-sm">
                <div className="p-0">
                  {apiKeysLoading ? (
                    <div className="flex justify-center items-center py-8">
                      <FaSpinner className="animate-spin mr-2" />
                      <span className="ml-2">Loading API keys...</span>
                    </div>
                  ) : apiKeysError ? (
                    <div className="text-center py-8">
                      <h6 className="text-lg font-semibold text-gray-900 mb-2">Error loading API keys</h6>
                      <p className="text-gray-600 mb-4">
                        Something went wrong while fetching API keys.
                      </p>
                      <button
                        className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                        onClick={() => refetchApiKeys()}
                      >
                        Try Again
                      </button>
                    </div>
                  ) : apiKeys.length === 0 ? (
                    <div className="text-center py-8">
                      <h6 className="text-lg font-semibold text-gray-900 mb-2">No API keys found</h6>
                      <p className="text-gray-600 mb-4">
                        No API keys match your current search or none have been generated yet.
                      </p>
                      <button
                        className="px-4 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                        onClick={() => setShowApiKeyModal(true)}
                      >
                        Generate Your First API Key
                      </button>
                    </div>
                  ) : (
                    <>
                      <div className="overflow-hidden">
                        <CommonDataTable
                          columns={getApiKeysColumns(handleDeleteApiKey)}
                          data={apiKeys}
                          pagination={false}
                          highlightOnHover={true}
                        />
                      </div>

                      {/* Pagination Section */}
                      <div className="flex justify-between items-center p-4 border-t border-gray-200">
                        <div className="flex items-center gap-2">
                          <span className="text-gray-600 text-sm">Rows per page:</span>
                          <select
                            className="px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            value={apiKeyPageSize}
                            onChange={(e) => setApiKeyPageSize(parseInt(e.target.value))}
                          >
                            <option value={5}>5</option>
                            <option value={10}>10</option>
                            <option value={25}>25</option>
                            <option value={50}>50</option>
                          </select>
                        </div>

                        {apiKeyTotalPages > 1 && (
                          <CommonPagination
                            currentPage={apiKeyCurrentPage}
                            totalPages={apiKeyTotalPages}
                            onPageChange={setApiKeyCurrentPage}
                          />
                        )}
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Log Details Slider */}
      <LogDetailsSlider
        show={showLogDetails}
        onHide={() => setShowLogDetails(false)}
        logData={selectedLog}
      />
      {/* Chart Modal */}
      <ChartModal
        show={showChartModal}
        onHide={() => setShowChartModal(false)}
        chartType={selectedChartType}
        chartTitle={selectedChartTitle}
      />
      {/* Popover for Query Parameters and Request Body */}
      {popoverData.show && (
        <>
          {/* Backdrop to close popover */}
          <div
            className="fixed inset-0 z-40"
            onClick={handleHidePopover}
          />

          {/* Popover Content */}
          <div
            className="absolute z-50 bg-white border border-gray-200 rounded-lg shadow-lg max-w-md w-80"
            style={{
              top: popoverData.position.top,
              left: popoverData.position.left,
              maxHeight: '400px',
              overflow: 'auto'
            }}
          >
            <div className="p-4">
              <div className="flex justify-between items-center mb-3">
                <h4 className="text-sm font-semibold text-gray-900">
                  {popoverData.type === 'queryParameter' ? 'Query Parameters' : 'Request Body'}
                </h4>
                <button
                  onClick={handleHidePopover}
                  className="text-gray-400 hover:text-gray-600 text-lg leading-none"
                >
                  ×
                </button>
              </div>

              <div className="bg-gray-50 rounded-md p-3">
                <pre className="text-xs text-gray-800 whitespace-pre-wrap break-all">
                  {JSON.stringify(popoverData.data, null, 2)}
                </pre>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Generate API Key Modal */}
      <CommonModal
        show={showApiKeyModal}
        onHide={() => setShowApiKeyModal(false)}
        title="Generate New API Key"
        size="md"
      >
        <Formik
          initialValues={{
            keyName: '',
            environment: '',
            expiryDate: null,
            logRetentionValue: 30,
            logRetentionUnit: 'days'
          }}
          validationSchema={apiKeySchema}
          onSubmit={handleGenerateApiKey}
          enableReinitialize
        >
          {({ values, errors, touched, handleChange, handleBlur, handleSubmit, setFieldValue, isSubmitting }) => (
            <form onSubmit={handleSubmit}>
              <div className="space-y-4">
                {/* Key Name Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Key Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="keyName"
                    value={values.keyName}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    placeholder="Enter key name (e.g., Production Key)"
                    className={cn(
                      "w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
                      errors.keyName && touched.keyName
                        ? "border-red-300 focus:ring-red-500"
                        : "border-gray-300"
                    )}
                  />
                  {errors.keyName && touched.keyName && (
                    <p className="mt-1 text-xs text-red-600">{errors.keyName}</p>
                  )}
                </div>

                {/* Environment Select */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Environment <span className="text-red-500">*</span>
                  </label>
                  <CommonSelect
                    placeholder={commonEnvironmentsLoading ? "Loading environments..." : "Select Environment"}
                    value={commonEnvironments.find(env => env.value === values.environment) || null}
                    onChange={(selectedOption) => {
                      setFieldValue('environment', selectedOption?.value || '');
                    }}
                    options={commonEnvironments.map(env => ({
                      value: env.value,
                      label: env.label,
                      description: env.description
                    }))}
                    isLoading={commonEnvironmentsLoading}
                    isDisabled={commonEnvironmentsLoading}
                    formatOptionLabel={(option) => (
                      <div>
                        <div className="font-medium text-gray-900">{option.label}</div>
                        <div className="text-xs text-gray-500 mt-1">{option.description}</div>
                      </div>
                    )}
                    className={cn(
                      errors.environment && touched.environment && "border-red-300"
                    )}
                  />
                  {errors.environment && touched.environment && (
                    <p className="mt-1 text-xs text-red-600">{errors.environment}</p>
                  )}
                  
                  {/* Show description for selected environment */}
                  {values.environment && (
                    <div className="mt-2 p-2 bg-blue-50 rounded-md">
                      {(() => {
                        const selectedEnv = commonEnvironments.find(env => env.value === values.environment);
                        return selectedEnv ? (
                          <p className="text-sm text-blue-700">
                            <span className="font-medium">{selectedEnv.label}:</span> {selectedEnv.description}
                          </p>
                        ) : null;
                      })()}
                    </div>
                  )}
                </div>

                {/* Expiry Date Picker */}
                <div>
                  <CommonDatePicker
                    label="Expiry Date"
                    required
                    selected={values.expiryDate}
                    onChange={(date) => setFieldValue('expiryDate', date)}
                    minDate={new Date()}
                    showTimeSelect
                    timeFormat="HH:mm"
                    timeIntervals={15}
                    dateFormat="dd/MM/yyyy HH:mm"
                    placeholder="Select expiry date and time"
                    error={errors.expiryDate && touched.expiryDate ? errors.expiryDate : ""}
                  />
                </div>

                {/* Log Retention */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Log Retention <span className="text-red-500">*</span>
                  </label>
                  <div className="flex gap-2">
                    {/* Number Input */}
                    <div className="flex-1">
                      <input
                        type="number"
                        name="logRetentionValue"
                        value={values.logRetentionValue}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        min="1"
                        placeholder="Enter value"
                        className={cn(
                          "w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
                          (errors.logRetentionValue && touched.logRetentionValue)
                            ? "border-red-300 focus:ring-red-500"
                            : "border-gray-300"
                        )}
                      />
                    </div>
                    
                    {/* Unit Dropdown */}
                    <div className="flex-1">
                      <select
                        name="logRetentionUnit"
                        value={values.logRetentionUnit}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        className={cn(
                          "w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
                          (errors.logRetentionUnit && touched.logRetentionUnit)
                            ? "border-red-300 focus:ring-red-500"
                            : "border-gray-300"
                        )}
                      >
                        <option value="hours">Hours</option>
                        <option value="days">Days</option>
                        <option value="months">Months</option>
                        <option value="years">Years</option>
                      </select>
                    </div>
                  </div>
                  
                  {/* Error Messages */}
                  {((errors.logRetentionValue && touched.logRetentionValue) || 
                    (errors.logRetentionUnit && touched.logRetentionUnit)) && (
                    <div className="mt-1">
                      {errors.logRetentionValue && touched.logRetentionValue && (
                        <p className="text-xs text-red-600">{errors.logRetentionValue}</p>
                      )}
                      {errors.logRetentionUnit && touched.logRetentionUnit && (
                        <p className="text-xs text-red-600">{errors.logRetentionUnit}</p>
                      )}
                    </div>
                  )}
                  
                  {/* Preview */}
                  {values.logRetentionValue && values.logRetentionUnit && (
                    <div className="mt-1">
                      <p className="text-xs text-gray-500">
                        Logs will be retained for: <span className="font-medium">
                          {values.logRetentionValue} {values.logRetentionUnit}
                        </span>
                      </p>
                    </div>
                  )}
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowApiKeyModal(false)}
                    className="px-4 py-2 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting || isGeneratingApiKey}
                    className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting || isGeneratingApiKey ? (
                      <div className="flex items-center">
                        <FaSpinner className="animate-spin mr-2" />
                        Generating...
                      </div>
                    ) : (
                      'Generate API Key'
                    )}
                  </button>
                </div>
              </div>
            </form>
          )}
        </Formik>
      </CommonModal>

      {/* Log Details Slider */}
      <LogDetailsSlider
        show={showLogDetailsSlider}
        onHide={handleCloseLogDetail}
        logData={logDetail}
      />
    </PrivateLayout>
  );
};

export default ProjectDetails;
