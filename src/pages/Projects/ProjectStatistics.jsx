import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { FaArrowLeft, FaEdit, FaCheck, FaTimes } from 'react-icons/fa';
import { useGetProjectDetailsMutation, useGetProjectStatisticsMutation, useCreateUpdateProjectMutation } from '../../features/projects/projectApi';
import { cn } from '../../utils/cn';
import CommonSelect from '../../components/CommonSelect';
import { showToast } from '../../utils/toast';

const ProjectStatistics = ({ 
  selectedEnvironment, 
  onEnvironmentChange, 
  environmentOptions, 
  environmentsLoading, 
  environments 
}) => {
    const navigate = useNavigate();
    const projectId = useParams().id;
    const [getProjectDetails, { data: projectDetails }] = useGetProjectDetailsMutation();
    const [getProjectStatistics, { data: projectStatistics }] = useGetProjectStatisticsMutation();
    const [createUpdateProject, { isLoading: isUpdating }] = useCreateUpdateProjectMutation();

    // Inline edit state - single state for all fields
    const [isEditingProject, setIsEditingProject] = useState(false);
    
    // Edit form values
    const [editName, setEditName] = useState('');
    const [editCode, setEditCode] = useState('');
    const [editType, setEditType] = useState([]);
    const [editLogEnabled, setEditLogEnabled] = useState(false);

    useEffect(() => {
        if(projectId){
            getProjectDetails({ projectId });
            getProjectStatistics({ projectId });
        }
    }, [projectId]);

    // Initialize edit values when project details are loaded
    useEffect(() => {
        if (projectDetails?.data) {
            setEditName(projectDetails.data.name || '');
            setEditCode(projectDetails.data.projectCode || '');
            setEditType(projectDetails.data.projectType || []);
            setEditLogEnabled(projectDetails.data.logEnabled || false);
        }
    }, [projectDetails]);

    // Handle edit actions
    const handleStartEdit = () => {
        setIsEditingProject(true);
        // Initialize all edit values
        setEditName(projectDetails?.data?.name || '');
        setEditCode(projectDetails?.data?.projectCode || '');
        setEditType(projectDetails?.data?.projectType || []);
        setEditLogEnabled(projectDetails?.data?.logEnabled || false);
    };

    const handleCancelEdit = () => {
        setIsEditingProject(false);
        // Reset all values to original
        setEditName(projectDetails?.data?.name || '');
        setEditCode(projectDetails?.data?.projectCode || '');
        setEditType(projectDetails?.data?.projectType || []);
        setEditLogEnabled(projectDetails?.data?.logEnabled || false);
    };

    const handleSaveEdit = async () => {
        try {
            // Validate all fields
            if (!editName.trim()) {
                showToast('error', 'Project name cannot be empty');
                return;
            }
            if (!editCode.trim()) {
                showToast('error', 'Project code cannot be empty');
                return;
            }
            if (editType.length === 0) {
                showToast('error', 'At least one project type must be selected');
                return;
            }

            // Create payload with all fields
            const payload = {
                projectId,
                name: editName.trim(),
                projectCode: editCode.trim(),
                projectType: editType,
                logEnabled: editLogEnabled
            };

            await createUpdateProject(payload).unwrap();
            
            // Refresh project details
            await getProjectDetails({ projectId });
            
            // Close edit mode
            setIsEditingProject(false);
    
        } catch (error) {
            showToast('error', error?.data?.message || 'Failed to update project');
        }
    };

    const handleProjectTypeChange = (type, checked) => {
        if (checked) {
            setEditType([...editType, type]);
        } else {
            setEditType(editType.filter(t => t !== type));
        }
    };


    return (
        <div className="mb-8">
        <button 
          className="flex items-center text-blue-600 hover:text-blue-800 mb-2 px-0 bg-transparent border-none cursor-pointer transition-colors"
          onClick={() => navigate('/projects')}
        >
          <FaArrowLeft className="mr-2" />Back to Projects
        </button>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <h2 className="text-2xl font-bold mb-0 mr-2">{projectDetails?.data?.name || "--"}</h2>
          </div>
          
          {/* Environment Selection */}
          <div className="w-64">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Environment
            </label>
            <CommonSelect
              placeholder="Select Environment"
              value={selectedEnvironment}
              onChange={onEnvironmentChange}
              options={environmentOptions}
              isLoading={environmentsLoading}
              isDisabled={environmentsLoading || environments?.environments?.length === 0}
            />
          </div>
        </div>
        <div className="flex flex-wrap items-center justify-between mb-6 p-6 bg-slate-50 rounded-xl border border-dashed border-slate-300">
          <div className="text-center min-w-[180px]">
            <div className="text-3xl font-bold text-blue-600">{projectStatistics?.data?.statistics?.uniqueApis ? projectStatistics?.data?.statistics?.uniqueApis :"--"}</div>
            <div className="text-sm text-gray-500">Unique APIs</div>
          </div>
          <div className="text-center min-w-[180px]">
            <div className="text-3xl font-bold text-green-600">{projectStatistics?.data?.statistics?.totalRequests ? projectStatistics?.data?.statistics?.totalRequests :"--"}</div>
            <div className="text-sm text-gray-500">Total Requests</div>
          </div>
          <div className="text-center min-w-[180px]">
            <div className="text-3xl font-bold text-cyan-600">{projectStatistics?.data?.statistics?.totalUsers ? projectStatistics?.data?.statistics?.totalUsers :"--"}</div>
            <div className="text-sm text-gray-500">Total Users</div>
          </div>
          <div className="text-center min-w-[180px]">
            <div className="text-3xl font-bold text-yellow-600">{projectStatistics?.data?.statistics?.successRate ? projectStatistics?.data?.statistics?.successRate :"--"}%</div>
            <div className="text-sm text-gray-500">Success Rate</div>
          </div>
          <div>
            <select className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" defaultValue="Aug-2024 - Jul-2025">
              <option>Aug-2024 - Jul-2025</option>
              <option>Jul-2023 - Jul-2024</option>
            </select>
          </div>
        </div>
        <div className="p-6 mb-6 bg-white rounded-xl shadow-sm">
          <div className="flex justify-between items-start mb-4">
            <h3 className="text-lg font-semibold text-gray-800">Project Details</h3>
            {!isEditingProject ? (
              <button
                onClick={handleStartEdit}
                className="px-3 py-1 border border-gray-800 text-gray-800 rounded hover:bg-gray-800 hover:text-white transition-colors flex items-center"
              >
                <FaEdit className="mr-1" size={12} />
                Edit
              </button>
            ) : (
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleSaveEdit}
                  disabled={isUpdating}
                  className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center"
                >
                  <FaCheck className="mr-1" size={12} />
                  Save
                </button>
                <button
                  onClick={handleCancelEdit}
                  disabled={isUpdating}
                  className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center"
                >
                  <FaTimes className="mr-1" size={12} />
                  Cancel
                </button>
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Project Name */}
            <div>
              <span className="font-semibold text-gray-600">Project Name:</span>
              {isEditingProject ? (
                <input
                  type="text"
                  value={editName}
                  onChange={(e) => setEditName(e.target.value)}
                  className="w-full mt-1 border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter project name"
                />
              ) : (
                <div className="mt-1 text-gray-900">{projectDetails?.data?.name || "--"}</div>
              )}
            </div>

            {/* Project ID */}
            <div>
              <span className="font-semibold text-gray-600">Project ID:</span>
              {isEditingProject ? (
                <input
                  type="text"
                  value={editCode}
                  onChange={(e) => setEditCode(e.target.value)}
                  className="w-full mt-1 border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter project code"
                />
              ) : (
                <div className="mt-1 text-gray-900">{projectDetails?.data?.projectCode || "--"}</div>
              )}
            </div>

            {/* Project Type */}
            <div>
              <span className="font-semibold text-gray-600">Project Type:</span>
              {isEditingProject ? (
                <div className="mt-1 flex flex-wrap gap-2">
                  {['Web', 'Mobile', 'Desktop'].map((type) => (
                    <label key={type} className="flex items-center text-sm">
                      <input
                        type="checkbox"
                        checked={editType.includes(type)}
                        onChange={(e) => handleProjectTypeChange(type, e.target.checked)}
                        className="mr-1"
                      />
                      {type}
                    </label>
                  ))}
                </div>
              ) : (
                <div className="mt-1">
                  {projectDetails?.data?.projectType?.length > 0 ? (
                    projectDetails.data.projectType.map((type, index) => (
                      <span key={index} className="inline-block bg-blue-600 text-white text-xs px-2 py-1 rounded mr-1">
                        {type}
                      </span>
                    ))
                  ) : (
                    <span className="text-gray-900">--</span>
                  )}
                </div>
              )}
            </div>

            {/* Log Enabled */}
            <div>
              <span className="font-semibold text-gray-600">Log Enabled:</span>
              {isEditingProject ? (
                <select
                  value={editLogEnabled}
                  onChange={(e) => setEditLogEnabled(e.target.value === 'true')}
                  className="w-full mt-1 border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="true">Yes</option>
                  <option value="false">No</option>
                </select>
              ) : (
                <div className="mt-1">
                  <span className={cn(
                    "inline-block text-xs px-2 py-1 rounded",
                    projectDetails?.data?.logEnabled 
                      ? "bg-green-600 text-white" 
                      : "bg-red-600 text-white"
                  )}>
                    {projectDetails?.data?.logEnabled ? "Yes" : "No"}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
}

export default ProjectStatistics;