import React, { useState, useEffect } from "react";
import PrivateLayout from "../../components/PrivateLayout";
import {
  FaStar,
  FaRegStar,
  FaTrash,
  FaPlus,
  FaGlobe,
  FaMobileAlt,
  FaDesktop,
  Fa<PERSON><PERSON>ch,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "react-icons/fa";
import { Formik } from "formik";
import CommonSelect from "../../components/CommonSelect";
import CommonModal from "../../components/CommonModal";
import CommonPagination from "../../components/CommonPagination";
import { useNavigate } from "react-router-dom";
import { confirmDialog } from "../../utils/alert";
import { showToast, ToastContainer } from "../../utils/toast";
import {
  useGetProjectListingQuery,
  useCreateUpdateProjectMutation,
  useToggleProjectFavouriteMutation,
  useDeleteProjectMutation,
} from "../../features/projects/projectApi";
import { projectSchema } from "../../validations/projectSchema";
import { cn } from "../../utils/cn";

const statusOptions = [
  { value: "all", label: "All Status" },
  { value: true, label: "Active" },
  { value: false, label: "Inactive" },
];

const TABS = [
  { key: "favourite", label: "Favourite Projects" },
  { key: "all", label: "All Projects" },
];

const PAGE_SIZE = 6;

const Projects = () => {
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState(statusOptions[0]);
  const [tab, setTab] = useState("favourite");
  const [showModal, setShowModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [editingProject, setEditingProject] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const navigate = useNavigate();

  // API query parameters
  const queryParams = {
    page: currentPage,
    pagesize: PAGE_SIZE,
    searchText: search,
  };

  // Only add favourite and status filters when on favourite tab
  if (tab === "favourite") {
    queryParams.isFavourite = true;
    queryParams.status = statusFilter.value === "all" ? true : statusFilter.value;
  }

  // Debug log to verify parameters
  console.log('Query Parameters:', queryParams, 'Tab:', tab);

  // API hooks
  const {
    data: projectData,
    isLoading,
    isError,
    error,
    refetch,
  } = useGetProjectListingQuery(queryParams);

  const [createUpdateProject] = useCreateUpdateProjectMutation();
  const [toggleFavourite] = useToggleProjectFavouriteMutation();
  const [deleteProject] = useDeleteProjectMutation();

  // Extract data from API response
  const projects = projectData?.projects || [];
  const totalPages = Math.ceil((projectData?.totalCount || 0) / PAGE_SIZE);
  const favouriteCount = projectData?.favouriteCount || 0;

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [search, statusFilter, tab]);

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setCurrentPage(1);
    }, 300);
    return () => clearTimeout(timeoutId);
  }, [search]);

  // Form initial values
  const getInitialValues = () => ({
    name: editingProject?.name || "",
    projectCode: editingProject?.projectCode || "",
    projectType: editingProject?.projectType || [],
    // logRetention: editingProject?.logRetention || 30,
    logEnabled: editingProject?.logEnabled || false,
    status: editingProject?.status !== undefined ? editingProject.status : true,
  });

  // Handle form submission
  const handleFormSubmit = async (values, { setSubmitting, resetForm }) => {
    try {
      const payload = {
        ...values,
        ...(isEditMode && { projectId: editingProject._id }),
      };

      await createUpdateProject(payload).unwrap();

      handleCloseModal();
      resetForm();
    } catch (error) {
      showToast(
        "error",
        `Failed to ${isEditMode ? "update" : "create"} project.`
      );
    } finally {
      setSubmitting(false);
    }
  };

  // Handle modal close
  const handleCloseModal = () => {
    setShowModal(false);
    setEditingProject(null);
    setIsEditMode(false);
  };

  // Handle add new project
  const handleAddProject = () => {
    setIsEditMode(false);
    setEditingProject(null);
    setShowModal(true);
  };

  // Handle edit project
  const handleEditProject = (project) => {
    setIsEditMode(true);
    setEditingProject(project);
    setShowModal(true);
  };

  // Toggle favourite
  const handleToggleFavourite = async (projectId, isFavourite) => {
    try {
      const payload = {
        projectId: projectId,
        isFavourite: !isFavourite,
      };
      await toggleFavourite(payload);
    } catch (error) {
      showToast("error", "Failed to update favourite status.");
    }
  };



  // Card platform icons
  const renderPlatforms = (projectTypes) => {
    if (!projectTypes || projectTypes.length === 0) return null;
    return (
      <div className="mb-2 flex flex-wrap gap-1">
        {projectTypes.includes("Web") && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <FaGlobe className="mr-1" />
            Web
          </span>
        )}
        {projectTypes.includes("Mobile") && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <FaMobileAlt className="mr-1" />
            Mobile
          </span>
        )}
        {projectTypes.includes("Desktop") && (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <FaDesktop className="mr-1" />
            Desktop
          </span>
        )}
      </div>
    );
  };

  // Handle delete project
  const handleDeleteProject = async (project, e) => {
    e.stopPropagation(); // Prevent card click navigation
    
    const confirmed = await confirmDialog({
      title: "Delete Project",
      text: `Are you sure you want to delete "${project.name}"? This action cannot be undone.`,
      icon: "warning",
      confirmButtonText: "Delete",
    });
    
    if (confirmed) {
      try {
        const payload = {
          projectIds: [project._id],
        };
        await deleteProject(payload).unwrap();
        showToast("success", "Project deleted successfully.");
      } catch (error) {
        showToast("error", "Failed to delete project.");
      }
    }
  };

  // Handle card click to navigate to project details
  const handleCardClick = (projectId) => {
    navigate(`/projects/${projectId}`);
  };

  // Modal content (Add/Edit Project)
  const renderModalContent = () => (
    <Formik
      initialValues={getInitialValues()}
      validationSchema={projectSchema}
      onSubmit={handleFormSubmit}
      enableReinitialize
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        handleSubmit,
        isSubmitting,
        setFieldValue,
      }) => (
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Project Name *
              </label>
              <input
                type="text"
                name="name"
                value={values.name}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder="Enter project name"
                className={cn(
                  "w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500",
                  touched.name && errors.name
                    ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:border-blue-500"
                )}
              />
              {touched.name && errors.name && (
                <div className="text-red-500 text-sm mt-1">{errors.name}</div>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Project Code *
              </label>
              <input
                type="text"
                name="projectCode"
                value={values.projectCode}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder="Enter project code"
                className={cn(
                  "w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500",
                  touched.projectCode && errors.projectCode
                    ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:border-blue-500"
                )}
              />
              {touched.projectCode && errors.projectCode && (
                <div className="text-red-500 text-sm mt-1">
                  {errors.projectCode}
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Project Type *
              </label>
              <div className="flex flex-wrap gap-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={values.projectType.includes("Web")}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setFieldValue("projectType", [
                          ...values.projectType,
                          "Web",
                        ]);
                      } else {
                        setFieldValue(
                          "projectType",
                          values.projectType.filter((type) => type !== "Web")
                        );
                      }
                    }}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Web</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={values.projectType.includes("Mobile")}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setFieldValue("projectType", [
                          ...values.projectType,
                          "Mobile",
                        ]);
                      } else {
                        setFieldValue(
                          "projectType",
                          values.projectType.filter((type) => type !== "Mobile")
                        );
                      }
                    }}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Mobile</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={values.projectType.includes("Desktop")}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setFieldValue("projectType", [
                          ...values.projectType,
                          "Desktop",
                        ]);
                      } else {
                        setFieldValue(
                          "projectType",
                          values.projectType.filter(
                            (type) => type !== "Desktop"
                          )
                        );
                      }
                    }}
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Desktop</span>
                </label>
              </div>
              {touched.projectType && errors.projectType && (
                <div className="text-red-500 text-sm mt-1">
                  {errors.projectType}
                </div>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                name="status"
                value={values.status}
                onChange={(e) =>
                  setFieldValue("status", e.target.value === "true")
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={true}>Active</option>
                <option value={false}>Inactive</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            {/* <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Log Retention (Days) *
              </label>
              <input
                type="number"
                name="logRetention"
                value={values.logRetention}
                onChange={handleChange}
                onBlur={handleBlur}
                min={1}
                max={365}
                className={cn(
                  "w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500",
                  touched.logRetention && errors.logRetention
                    ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:border-blue-500"
                )}
              />
              {touched.logRetention && errors.logRetention && (
                <div className="text-red-500 text-sm mt-1">
                  {errors.logRetention}
                </div>
              )}
            </div> */}
            <div>
              <label className="flex items-center mt-6">
                <input
                  type="checkbox"
                  name="logEnabled"
                  checked={values.logEnabled}
                  onChange={handleChange}
                  className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm font-medium text-gray-700">
                  Log Enabled
                </span>
              </label>
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-6">
            <button
              type="button"
              onClick={handleCloseModal}
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 flex items-center"
            >
              {isSubmitting ? (
                <>
                  <FaSpinner className="animate-spin mr-2" />
                  {isEditMode ? "Updating..." : "Creating..."}
                </>
              ) : isEditMode ? (
                "Update Project"
              ) : (
                "Create Project"
              )}
            </button>
          </div>
        </form>
      )}
    </Formik>
  );

  // Loading state
  if (isLoading) {
    return (
      <PrivateLayout>
        <div
          className="d-flex justify-content-center align-items-center"
          style={{ minHeight: "400px" }}
        >
          <FaSpinner className="animate-spin mr-2" />
          <span className="ms-2">Loading projects...</span>
        </div>
      </PrivateLayout>
    );
  }

  // Error state
  if (isError) {
    return (
      <PrivateLayout>
        <div className="text-center py-5">
          <h4>Error loading projects</h4>
          <p className="text-muted">
            {error?.message || "Something went wrong"}
          </p>
          <button variant="primary" onClick={() => refetch()}>
            Try Again
          </button>
        </div>
      </PrivateLayout>
    );
  }

  return (
    <PrivateLayout>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Projects</h2>
        <button
          onClick={handleAddProject}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
        >
          <FaPlus className="mr-2" />
          Add New Project
        </button>
      </div>

      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center mb-4">
          <div className="md:col-span-7">
            <input
              type="text"
              placeholder="Search projects..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div className="md:col-span-3">
            <CommonSelect
              options={statusOptions}
              value={statusFilter}
              onChange={setStatusFilter}
              placeholder="All Status"
            />
          </div>
        </div>

        {/* Tabs */}
        <div className="flex items-center border-b border-gray-200 mb-6">
          {TABS.map((t) => (
            <button
              key={t.key}
              onClick={() => setTab(t.key)}
              className={`px-4 py-3 font-semibold text-sm border-b-2 transition-colors ${
                tab === t.key
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-600 hover:text-gray-900 hover:border-gray-300"
              }`}
            >
              {t.label}
              {t.key === "favourite" && favouriteCount > 0 && (
                <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  {favouriteCount}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {projects.length === 0 ? (
          <div className="col-span-full text-center text-gray-500 py-12">
            {search || statusFilter.value !== "all"
              ? "No projects found matching your criteria."
              : "No projects found."}
          </div>
        ) : (
          projects.map((project) => (
            <div
              key={project._id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => handleCardClick(project._id)}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="font-semibold text-gray-600">
                  {project.projectCode}
                </div>
                <button
                  className="p-2 text-red-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors focus:outline-none"
                  onClick={(e) => handleDeleteProject(project, e)}
                  title="Delete Project"
                >
                  <FaTrash size={14} />
                </button>
              </div>

              <div className="flex items-center justify-between mb-3">
                <div className="text-xl font-bold text-gray-900">
                  {project.name}
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleToggleFavourite(project._id, project.isFavourite);
                  }}
                  className="p-1 hover:scale-110 transition-transform"
                >
                  {project.isFavourite ? (
                    <FaStar color="#ffc107" size={22} />
                  ) : (
                    <FaRegStar color="#adb5bd" size={22} />
                  )}
                </button>
              </div>

              <div className="flex gap-2 mb-4">
                {project.status ? (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                  </span>
                ) : (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    Inactive
                  </span>
                )}
                {project.logEnabled ? (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Log Enabled
                  </span>
                ) : (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    Log Disabled
                  </span>
                )}
              </div>

              {renderPlatforms(project.projectType)}

              {/* Environment-wise Statistics */}
              {project.statistics && project.statistics.length > 0 ? (
                <div className="mb-4">
                  <div className="text-xs text-gray-500 mb-2">Environment Statistics</div>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {project.statistics.slice(0, 3).map((stat, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-2">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs font-medium text-gray-700 capitalize">
                            {stat.env}
                          </span>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            stat.successRate >= 95 
                              ? 'bg-green-100 text-green-800' 
                              : stat.successRate >= 80 
                                ? 'bg-yellow-100 text-yellow-800' 
                                : 'bg-red-100 text-red-800'
                          }`}>
                            {stat.successRate.toFixed(1)}%
                          </span>
                        </div>
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          <div>
                            <div className="text-gray-500">APIs</div>
                            <div className="font-semibold">{stat.uniqueApis.toLocaleString()}</div>
                          </div>
                          <div>
                            <div className="text-gray-500">Requests</div>
                            <div className="font-semibold">{stat.totalRequests.toLocaleString()}</div>
                          </div>
                          <div>
                            <div className="text-gray-500">Users</div>
                            <div className="font-semibold">{stat.totalUsers.toLocaleString()}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                    {project.statistics.length > 3 && (
                      <div className="text-center">
                        <span className="text-xs text-gray-500">
                          +{project.statistics.length - 3} more environments
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div>
                    <div className="text-xs text-gray-500">APIs</div>
                    <div className="font-semibold">--</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Requests</div>
                    <div className="font-semibold">--</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Users</div>
                    <div className="font-semibold">--</div>
                  </div>
                </div>
              )}

              {/* Overall Success Rate */}
              <div className="mb-4">
                <div className="text-xs text-gray-500 mb-2">
                  Overall Success Rate
                </div>
                <div className="flex items-center">
                  <div className="flex-1 bg-gray-200 rounded-full h-2 mr-3">
                    <div
                      className="bg-green-500 h-2 rounded-full transition-all duration-300"
                      style={{ 
                        width: project.statistics && project.statistics.length > 0 
                          ? `${(project.statistics.reduce((acc, stat) => acc + stat.successRate, 0) / project.statistics.length).toFixed(1)}%`
                          : "0%" 
                      }}
                    ></div>
                  </div>
                  <span className="font-bold text-green-600">
                    {project.statistics && project.statistics.length > 0 
                      ? `${(project.statistics.reduce((acc, stat) => acc + stat.successRate, 0) / project.statistics.length).toFixed(1)}%`
                      : "--"}
                  </span>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <div>
                  <div className="text-xs text-gray-500">Created Date</div>
                  <div className="font-semibold text-gray-900">
                    {new Date(project.createdAt).toLocaleDateString()}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-xs text-gray-500">Status</div>
                  <div className="font-semibold text-gray-900">
                    {project.status ? "Active" : "Inactive"}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <CommonPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      )}

      <CommonModal
        show={showModal}
        size="xl"
        onHide={handleCloseModal}
        title={isEditMode ? "Edit Project" : "Add New Project"}
      >
        {renderModalContent()}
      </CommonModal>

      <ToastContainer position="top-right" autoClose={3000} />
    </PrivateLayout>
  );
};

export default Projects;
