import React from 'react';
import { Link } from 'react-router-dom';
import { FaProjectDiagram, FaFileAlt, FaExclamationTriangle, FaHeartbeat, FaPlus, FaUsers, FaUser, FaSignInAlt } from 'react-icons/fa';
import PrivateLayout from '../../components/PrivateLayout';
import { useEffect, useRef, useState } from 'react';
import { cn } from '../../utils/cn';

// Animated count-up hook
function useCountUp(end, duration = 1200) {
  const [count, setCount] = useState(0);
  const start = useRef(null);
  useEffect(() => {
    let frame;
    function animate(ts) {
      if (!start.current) start.current = ts;
      const progress = Math.min((ts - start.current) / duration, 1);
      setCount(Math.floor(progress * end));
      if (progress < 1) {
        frame = requestAnimationFrame(animate);
      } else {
        setCount(end);
      }
    }
    frame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(frame);
  }, [end, duration]);
  return count;
}

// StatCard component
const StatCard = ({ title, value, icon: Icon, color, tooltip, gradient, className }) => {
  const count = useCountUp(value);
  return (
    <div 
      className={cn(
        "relative overflow-hidden rounded-lg shadow-sm border border-gray-200 p-6 transition-all duration-200 hover:shadow-md cursor-pointer group",
        className
      )}
      style={{ background: gradient }}
      title={tooltip}
    >
      <div className="flex items-center justify-between">
        <div>
          <div className="text-sm font-medium text-white/90 mb-1">{title}</div>
          <div className="text-3xl font-bold text-white">{count.toLocaleString()}</div>
        </div>
        <div className="text-white/80 group-hover:text-white transition-colors">
          <Icon size={40} />
        </div>
      </div>
      <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity"></div>
    </div>
  );
};

const Dashboard = () => {
  return (
    <PrivateLayout>
      <h1 className="text-3xl font-bold mb-6">Dashboard</h1>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Projects"
          value={12}
          icon={FaProjectDiagram}
          color="#2563eb"
          tooltip="Total number of projects in the system."
          gradient="linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"
        />
        <StatCard
          title="Active Logs"
          value={2847}
          icon={FaFileAlt}
          color="#059669"
          tooltip="Number of active log entries."
          gradient="linear-gradient(135deg, #10b981 0%, #059669 100%)"
        />
        <StatCard
          title="Errors Today"
          value={18}
          icon={FaExclamationTriangle}
          color="#f59e42"
          tooltip="Errors detected in the last 24 hours."
          gradient="linear-gradient(135deg, #f59e0b 0%, #d97706 100%)"
        />
        <StatCard
          title="System Health"
          value={95}
          icon={FaHeartbeat}
          color="#2563eb"
          tooltip="Overall system health percentage."
          gradient="linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)"
        />
      </div>
      {/* Recent Activity and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h6 className="text-lg font-semibold text-gray-900">Recent Log Activity</h6>
            </div>
            <div className="p-6">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Time</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Project</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Level</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Message</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                      <td className="py-3 px-4 text-sm text-gray-600">2 min ago</td>
                      <td className="py-3 px-4 text-sm text-gray-900">E-commerce App</td>
                      <td className="py-3 px-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          ERROR
                        </span>
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-600">Database connection failed</td>
                      <td className="py-3 px-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          Pending
                        </span>
                      </td>
                    </tr>
                    <tr className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                      <td className="py-3 px-4 text-sm text-gray-600">5 min ago</td>
                      <td className="py-3 px-4 text-sm text-gray-900">API Gateway</td>
                      <td className="py-3 px-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          INFO
                        </span>
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-600">Request processed successfully</td>
                      <td className="py-3 px-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Resolved
                        </span>
                      </td>
                    </tr>
                    <tr className="hover:bg-gray-50 transition-colors">
                      <td className="py-3 px-4 text-sm text-gray-600">10 min ago</td>
                      <td className="py-3 px-4 text-sm text-gray-900">User Service</td>
                      <td className="py-3 px-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          WARN
                        </span>
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-600">High memory usage detected</td>
                      <td className="py-3 px-4">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          Pending
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h6 className="text-lg font-semibold text-gray-900">Quick Actions</h6>
            </div>
            <div className="p-6">
              <div className="space-y-3">
                <Link
                  to="/projects"
                  className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  <FaPlus className="mr-2" />Add New Project
                </Link>
                <Link
                  to="/projects"
                  className="w-full flex items-center justify-center px-4 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors font-medium"
                >
                  <FaProjectDiagram className="mr-2" />View Projects
                </Link>
                <Link
                  to="/manage-users"
                  className="w-full flex items-center justify-center px-4 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors font-medium"
                >
                  <FaUsers className="mr-2" />Manage Users
                </Link>
                <Link
                  to="/manage-profile"
                  className="w-full flex items-center justify-center px-4 py-3 border border-blue-600 text-blue-600 rounded-lg hover:bg-blue-50 transition-colors font-medium"
                >
                  <FaUser className="mr-2" />Manage Profile
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Floating Action Button */}
      <Link
        to="/projects"
        className="fixed bottom-6 right-6 w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-50"
        title="Add New Project"
      >
        <FaPlus className="text-lg" />
      </Link>
    </PrivateLayout>
  );
};

export default Dashboard; 