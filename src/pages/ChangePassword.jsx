import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>a<PERSON><PERSON>, FaEyeSlash, FaLock, FaArrowLeft, FaShieldAlt, FaSpinner } from "react-icons/fa";
import { useChangePasswordMutation } from "../features/auth/authApi";
import { showToast } from "../utils/toast";
import PrivateLayout from "../components/PrivateLayout";
import { cn } from "../utils/cn";

const ChangePassword = () => {
  const navigate = useNavigate();
  const [changePassword, { isLoading }] = useChangePasswordMutation();

  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const [passwordStrength, setPasswordStrength] = useState("");
  const [passwordRequirements, setPasswordRequirements] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false,
  });

  const [logoutAllDevices, setLogoutAllDevices] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Check password strength for new password
    if (name === "newPassword") {
      checkPasswordStrength(value);
    }
  };

  const checkPasswordStrength = (password) => {
    const requirements = {
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /\d/.test(password),
      special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
    };

    setPasswordRequirements(requirements);

    const score = Object.values(requirements).filter(Boolean).length;
    if (score === 0) setPasswordStrength("");
    else if (score <= 2) setPasswordStrength("Very Weak");
    else if (score === 3) setPasswordStrength("Weak");
    else if (score === 4) setPasswordStrength("Good");
    else setPasswordStrength("Strong");
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const validateForm = () => {
    if (!formData.currentPassword) {
      showToast("error", "Current password is required");
      return false;
    }

    if (!formData.newPassword) {
      showToast("error", "New password is required");
      return false;
    }

    if (formData.newPassword.length < 8) {
      showToast("error", "Password must be at least 8 characters long");
      return false;
    }

    if (!Object.values(passwordRequirements).every(Boolean)) {
      showToast("error", "Password does not meet all requirements");
      return false;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      showToast("error", "New passwords do not match");
      return false;
    }

    if (formData.currentPassword === formData.newPassword) {
      showToast(
        "error",
        "New password must be different from current password"
      );
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      const result = await changePassword({
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword,
      }).unwrap();

      // Navigate back to profile after a short delay
      setTimeout(() => {
        navigate("/manage-profile");
      }, 500);
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <PrivateLayout>
      <div className="min-h-[calc(100vh-166px)] bg-white relative overflow-hidden flex items-center justify-center">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-40 animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-cyan-100 rounded-full mix-blend-multiply filter blur-xl opacity-40 animate-pulse animation-delay-200"></div>
          <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-sky-100 rounded-full mix-blend-multiply filter blur-xl opacity-40 animate-pulse animation-delay-400"></div>
        </div>

        {/* Floating particles effect */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-blue-700 rounded-full opacity-20 animate-pulse"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${3 + Math.random() * 2}s`
              }}
            />
          ))}
        </div>

        {/* Main Content */}
        <div className="relative z-10 flex items-center justify-center py-8">
          <div className="container mx-auto px-4">
            <div className="grid lg:grid-cols-2 gap-8 items-center max-w-6xl mx-auto">
              {/* Left Side - Password Security Tips (First on desktop) */}
              <div className="bg-white border border-gray-200 rounded-xl shadow-lg order-2 lg:order-1 overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                  <h5 className="text-lg font-semibold text-gray-800 flex items-center">
                    <FaShieldAlt className="mr-2 text-blue-600" />
                    Password Security Tips
                  </h5>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                        <span className="text-green-600 text-sm font-bold">✓</span>
                      </div>
                      <span className="text-gray-700">
                        Use a unique password that you don't use for other
                        accounts
                      </span>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                        <span className="text-green-600 text-sm font-bold">✓</span>
                      </div>
                      <span className="text-gray-700">
                        Consider using a password manager to generate and store
                        strong passwords
                      </span>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                        <span className="text-green-600 text-sm font-bold">✓</span>
                      </div>
                      <span className="text-gray-700">
                        Enable two-factor authentication for additional security
                      </span>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                        <span className="text-green-600 text-sm font-bold">✓</span>
                      </div>
                      <span className="text-gray-700">
                        Regularly update your password and never share it with
                        others
                      </span>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                        <span className="text-green-600 text-sm font-bold">✓</span>
                      </div>
                      <span className="text-gray-700">
                        Avoid using personal information like birthdays or names
                        in your password
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Side - Change Password Form (Second on desktop) */}
              <div className="bg-white border border-gray-200 rounded-xl shadow-lg order-1 lg:order-2 overflow-hidden">
                  <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-blue-700">
                    <h4 className="text-xl font-semibold text-white flex items-center">
                      <FaLock className="mr-2" />
                      Change Password
                    </h4>
                  </div>

                  <div className="p-6">
                    <form onSubmit={handleSubmit}>
                      {/* Current Password */}
                      <div className="mb-6">
                        <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-2">
                          Current Password <span className="text-red-500">*</span>
                        </label>
                        <div className="relative">
                          <input
                            type={showPasswords.current ? "text" : "password"}
                            id="currentPassword"
                            name="currentPassword"
                            value={formData.currentPassword}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:border-blue-500 focus:bg-blue-50/50 transition-all duration-200"
                            placeholder="Enter your current password"
                            required
                          />
                          <button
                            type="button"
                            onClick={() => togglePasswordVisibility("current")}
                            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none"
                          >
                            {showPasswords.current ? <FaEyeSlash /> : <FaEye />}
                          </button>
                        </div>
                      </div>

                      {/* New Password */}
                      <div className="mb-4">
                        <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-2">
                          New Password <span className="text-red-500">*</span>
                        </label>
                        <div className="relative">
                          <input
                            type={showPasswords.new ? "text" : "password"}
                            id="newPassword"
                            name="newPassword"
                            value={formData.newPassword}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:border-blue-500 focus:bg-blue-50/50 transition-all duration-200"
                            placeholder="Enter your new password"
                            required
                          />
                          <button
                            type="button"
                            onClick={() => togglePasswordVisibility("new")}
                            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none"
                          >
                            {showPasswords.new ? <FaEyeSlash /> : <FaEye />}
                          </button>
                        </div>

                        {formData.newPassword && (
                          <div className="mt-3">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-gray-600">Password strength:</span>
                              <span className={cn(
                                "text-sm font-medium",
                                passwordStrength === "Very Weak" && "text-red-500",
                                passwordStrength === "Weak" && "text-orange-500",
                                passwordStrength === "Good" && "text-yellow-500",
                                passwordStrength === "Strong" && "text-green-500"
                              )}>
                                {passwordStrength}
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className={cn(
                                  "h-2 rounded-full transition-all duration-300",
                                  passwordStrength === "Very Weak" && "bg-red-500",
                                  passwordStrength === "Weak" && "bg-orange-500",
                                  passwordStrength === "Good" && "bg-yellow-500",
                                  passwordStrength === "Strong" && "bg-green-500"
                                )}
                                style={{
                                  width:
                                    passwordStrength === "Very Weak"
                                      ? "20%"
                                      : passwordStrength === "Weak"
                                      ? "40%"
                                      : passwordStrength === "Good"
                                      ? "70%"
                                      : passwordStrength === "Strong"
                                      ? "100%"
                                      : "0%",
                                }}
                              ></div>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Password Requirements */}
                      {formData.newPassword && (
                        <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                          <p className="text-sm font-medium text-gray-700 mb-3">
                            Password Requirements:
                          </p>
                          <ul className="space-y-2">
                            <li className={cn(
                              "flex items-center text-sm",
                              passwordRequirements.length ? "text-green-600" : "text-red-500"
                            )}>
                              <span className="mr-2 font-bold">
                                {passwordRequirements.length ? "✓" : "✗"}
                              </span>
                              At least 8 characters
                            </li>
                            <li className={cn(
                              "flex items-center text-sm",
                              passwordRequirements.uppercase ? "text-green-600" : "text-red-500"
                            )}>
                              <span className="mr-2 font-bold">
                                {passwordRequirements.uppercase ? "✓" : "✗"}
                              </span>
                              At least one uppercase letter
                            </li>
                            <li className={cn(
                              "flex items-center text-sm",
                              passwordRequirements.lowercase ? "text-green-600" : "text-red-500"
                            )}>
                              <span className="mr-2 font-bold">
                                {passwordRequirements.lowercase ? "✓" : "✗"}
                              </span>
                              At least one lowercase letter
                            </li>
                            <li className={cn(
                              "flex items-center text-sm",
                              passwordRequirements.number ? "text-green-600" : "text-red-500"
                            )}>
                              <span className="mr-2 font-bold">
                                {passwordRequirements.number ? "✓" : "✗"}
                              </span>
                              At least one number
                            </li>
                            <li className={cn(
                              "flex items-center text-sm",
                              passwordRequirements.special ? "text-green-600" : "text-red-500"
                            )}>
                              <span className="mr-2 font-bold">
                                {passwordRequirements.special ? "✓" : "✗"}
                              </span>
                              At least one special character
                            </li>
                          </ul>
                        </div>
                      )}

                      {/* Confirm New Password */}
                      <div className="mb-4">
                        <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                          Confirm New Password{" "}
                          <span className="text-red-500">*</span>
                        </label>
                        <div className="relative">
                          <input
                            type={showPasswords.confirm ? "text" : "password"}
                            id="confirmPassword"
                            name="confirmPassword"
                            value={formData.confirmPassword}
                            onChange={handleInputChange}
                            className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:border-blue-500 focus:bg-blue-50/50 transition-all duration-200"
                            placeholder="Confirm your new password"
                            required
                          />
                          <button
                            type="button"
                            onClick={() => togglePasswordVisibility("confirm")}
                            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none"
                          >
                            {showPasswords.confirm ? <FaEyeSlash /> : <FaEye />}
                          </button>
                        </div>

                        {formData.confirmPassword &&
                          formData.newPassword !== formData.confirmPassword && (
                            <p className="mt-2 text-sm text-red-600">
                              Passwords do not match
                            </p>
                          )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex flex-col sm:flex-row gap-4">
                        <button
                          type="submit"
                          disabled={
                            isLoading ||
                            !Object.values(passwordRequirements).every(Boolean) ||
                            formData.newPassword !== formData.confirmPassword
                          }
                          className={cn(
                            "flex-1 flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg shadow-md transition-all duration-200",
                            "hover:from-blue-700 hover:to-blue-800 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                            "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:from-blue-600 disabled:hover:to-blue-700"
                          )}
                        >
                          {isLoading ? (
                            <>
                              <FaSpinner className="animate-spin mr-2" />
                              Changing Password...
                            </>
                          ) : (
                            <>
                              <FaLock className="mr-2" />
                              Change Password
                            </>
                          )}
                        </button>

                        <button
                          type="button"
                          onClick={() => navigate("/manage-profile")}
                          disabled={isLoading}
                          className={cn(
                            "flex-1 flex items-center justify-center px-6 py-3 bg-white text-gray-700 font-medium rounded-lg border border-gray-300 shadow-sm transition-all duration-200",
                            "hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                            "disabled:opacity-50 disabled:cursor-not-allowed"
                          )}
                        >
                          <FaArrowLeft className="mr-2" />
                          Back to Profile
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
    </PrivateLayout>
  );
};

export default ChangePassword;
