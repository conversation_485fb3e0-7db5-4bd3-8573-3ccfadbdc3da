import PrivateLayout from '../../components/PrivateLayout';

const ManageUsers = () => (
  <PrivateLayout>
    <div className="w-full">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center max-w-4xl mx-auto">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Manage Users</h2>
        <p className="text-gray-600 text-lg">Coming Soon...</p>
        <div className="mt-6">
          <div className="inline-flex items-center px-4 py-2 bg-blue-50 text-blue-700 rounded-lg">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
            Feature in development
          </div>
        </div>
      </div>
    </div>
  </PrivateLayout>
);
export default ManageUsers;