import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useLoginMutation } from '../features/auth/authApi';
import { loginUserTokenAction } from '../features/auth/token';
import { Link, useNavigate } from 'react-router-dom';
import { FaShieldAlt, FaCheckCircle, FaEnvelope, FaLock, FaEye, FaEyeSlash, FaSignInAlt, FaInfoCircle, FaExclamationTriangle } from 'react-icons/fa';
import { useFormik } from 'formik';
import { loginValidationSchema } from '../utils/schema';
import { cn } from '../utils/cn';

const Login = () => {
  // Remove useState for email/password
  // const [email, setEmail] = useState('<EMAIL>');
  // const [password, setPassword] = useState('password123');
  const [remember, setRemember] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [login, { isLoading, error }] = useLoginMutation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [showError, setShowError] = useState(false);

  const initialValues = {
    email: '<EMAIL>',
    password: 'Admin@1234',
  };

  const formik = useFormik({
    initialValues,
    validationSchema: loginValidationSchema,
    onSubmit: async (values) => {
      setShowError(false);
      try {
        const res = await login({ emailId: values.email, password: values.password }).unwrap();
        if (res.status) {
          navigate('/dashboard');
        }
      } catch (err) {
        setShowError(true);
      }
    },
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-200"></div>
        <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-sky-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-400"></div>
      </div>

      {/* Floating particles effect */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white rounded-full opacity-10 animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-6xl">
          {/* Logo and Branding */}
          <div className="text-center mb-8 animate-fade-in">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-white/10 backdrop-blur-lg rounded-2xl mb-6 shadow-2xl">
              <FaShieldAlt className="text-3xl text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-2">
              Log Management System
            </h1>
            <p className="text-xl text-blue-200">
              Enterprise-grade logging solution
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Side - Features */}
            <div className="hidden lg:block animate-fade-in">
              <div className="space-y-8">
                <div>
                  <h2 className="text-3xl font-bold text-white mb-6">
                    Why Choose Our Platform?
                  </h2>
                  <p className="text-lg text-blue-100 leading-relaxed">
                    Experience the power of real-time logging with enterprise-grade security
                    and analytics that scale with your business needs.
                  </p>
                </div>

                <div className="grid gap-6">
                  {[
                    {
                      icon: '⚡',
                      title: 'Real-time Monitoring',
                      description: 'Monitor your applications with millisecond precision and instant alerts.'
                    },
                    {
                      icon: '📊',
                      title: 'Advanced Analytics',
                      description: 'Deep insights with ML-powered anomaly detection and trend analysis.'
                    },
                    {
                      icon: '🔒',
                      title: 'Enterprise Security',
                      description: 'Bank-grade encryption with SOC2 compliance and audit trails.'
                    },
                    {
                      icon: '🚀',
                      title: 'Scalable Infrastructure',
                      description: 'Handle millions of log entries with auto-scaling architecture.'
                    }
                  ].map((feature, index) => (
                    <div
                      key={index}
                      className="group flex items-start space-x-4 p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 transition-all duration-300"
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="text-2xl">{feature.icon}</div>
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-1">{feature.title}</h3>
                        <p className="text-blue-200 text-sm">{feature.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Side - Login Form */}
            <div className="animate-fade-in">
              <div className="backdrop-blur-xl bg-white/10 border border-white/20 rounded-3xl p-8 md:p-10 shadow-2xl">
                <div className="text-center mb-8">
                  <h3 className="text-3xl font-bold text-white mb-2">Welcome Back</h3>
                  <p className="text-blue-200">Sign in to access your dashboard</p>
                </div>

                {/* Error Message */}
                {(error || showError) && (
                  <div className="bg-red-500/20 border border-red-400/30 rounded-xl p-4 mb-6 backdrop-blur-sm animate-slide-in">
                    <div className="flex items-start">
                      <FaExclamationTriangle className="mr-3 text-red-300 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-red-200 font-medium">Authentication Failed</p>
                        <p className="text-red-300 text-sm mt-1">
                          {error?.data?.message || 'Please check your credentials and try again.'}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Login Form */}
                <form onSubmit={formik.handleSubmit} className="space-y-6">
                  {/* Email Field */}
                  <div className="space-y-2">
                    <label htmlFor="email" className="block text-sm font-medium text-blue-200">
                      Email Address
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <FaEnvelope className={cn(
                          "transition-colors duration-200",
                          formik.touched.email && formik.errors.email ? "text-red-400" : "text-blue-300"
                        )} />
                      </div>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        placeholder="Enter your email address"
                        value={formik.values.email}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        className={cn(
                          "w-full pl-12 pr-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-blue-300 backdrop-blur-sm transition-all duration-200",
                          "focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent focus:border-blue-400 focus:bg-white/15",
                          "hover:bg-white/15 autofill-transparent",
                          formik.touched.email && formik.errors.email
                            ? "border-red-400/50 bg-red-500/10 focus:ring-red-400 focus:border-red-400"
                            : "border-white/20"
                        )}
                      />
                    </div>
                    {formik.touched.email && formik.errors.email && (
                      <p className="text-red-300 text-sm animate-slide-in">
                        {formik.errors.email}
                      </p>
                    )}
                  </div>

                  {/* Password Field */}
                  <div className="space-y-2">
                    <label htmlFor="password" className="block text-sm font-medium text-blue-200">
                      Password
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <FaLock className={cn(
                          "transition-colors duration-200",
                          formik.touched.password && formik.errors.password ? "text-red-400" : "text-blue-300"
                        )} />
                      </div>
                      <input
                        type={showPassword ? 'text' : 'password'}
                        id="password"
                        name="password"
                        placeholder="Enter your password"
                        value={formik.values.password}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        className={cn(
                          "w-full pl-12 pr-12 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-blue-300 backdrop-blur-sm transition-all duration-200",
                          "focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent focus:border-blue-400 focus:bg-white/15",
                          "hover:bg-white/15 autofill-transparent",
                          formik.touched.password && formik.errors.password
                            ? "border-red-400/50 bg-red-500/10 focus:ring-red-400 focus:border-red-400"
                            : "border-white/20"
                        )}
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-4 flex items-center"
                        onClick={() => setShowPassword(v => !v)}
                      >
                        {showPassword ? (
                          <FaEyeSlash className="text-blue-300 hover:text-white transition-colors duration-200" />
                        ) : (
                          <FaEye className="text-blue-300 hover:text-white transition-colors duration-200" />
                        )}
                      </button>
                    </div>
                    {formik.touched.password && formik.errors.password && (
                      <p className="text-red-300 text-sm animate-slide-in">
                        {formik.errors.password}
                      </p>
                    )}
                  </div>

                  {/* Remember Me & Forgot Password */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="relative">
                        <input
                          type="checkbox"
                          id="rememberMe"
                          name="rememberMe"
                          checked={remember}
                          onChange={e => setRemember(e.target.checked)}
                          className="sr-only"
                        />
                        <label
                          htmlFor="rememberMe"
                          className={cn(
                            "flex items-center cursor-pointer group"
                          )}
                        >
                          <div className={cn(
                            "relative w-5 h-5 rounded-lg border-2 transition-all duration-200",
                            "bg-white/10 backdrop-blur-sm shadow-inner",
                            remember
                              ? "border-blue-400 bg-gradient-to-br from-blue-500 to-cyan-500 shadow-lg"
                              : "border-white/30 hover:border-blue-300 hover:bg-white/15",
                            "group-focus-within:ring-2 group-focus-within:ring-blue-400/50"
                          )}>
                            {/* Custom checkmark */}
                            <div className={cn(
                              "absolute inset-0 flex items-center justify-center transition-all duration-200",
                              remember ? "opacity-100 scale-100" : "opacity-0 scale-50"
                            )}>
                              <svg
                                className="w-3 h-3 text-white"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                                strokeWidth={3}
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="M5 13l4 4L19 7"
                                />
                              </svg>
                            </div>

                            {/* Glow effect when checked */}
                            {remember && (
                              <div className="absolute inset-0 rounded-lg bg-blue-400/20 animate-pulse"></div>
                            )}
                          </div>
                          <span className="ml-3 text-sm text-blue-200 select-none transition-colors duration-200 group-hover:text-white">
                            Remember me for 30 days
                          </span>
                        </label>
                      </div>
                    </div>
                    <Link
                      to="/forgot-password"
                      className="text-sm text-blue-300 hover:text-white font-medium transition-colors duration-200"
                    >
                      Forgot password?
                    </Link>
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    disabled={isLoading || !formik.isValid}
                    className={cn(
                      "w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-200 transform",
                      "bg-gradient-to-r from-blue-500 to-cyan-500 text-white",
                      "hover:from-blue-600 hover:to-cyan-600 hover:scale-[1.02] hover:shadow-xl",
                      "focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent",
                      "active:scale-[0.98]",
                      (isLoading || !formik.isValid) && "opacity-50 cursor-not-allowed hover:scale-100"
                    )}
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-3"></div>
                        Signing In...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <FaSignInAlt className="mr-3" />
                        Sign In to Dashboard
                      </div>
                    )}
                  </button>
                </form>

                {/* Demo Credentials */}
                <div className="mt-8 p-6 bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl">
                  <div className="flex items-center mb-4">
                    <FaInfoCircle className="mr-2 text-blue-300" />
                    <h6 className="font-medium text-blue-200">Demo Account</h6>
                  </div>
                  <div className="space-y-3">
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                      <span className="text-sm font-medium text-blue-300 min-w-[5rem]">Email:</span>
                      <code className="text-sm text-white bg-black/20 px-3 py-1 rounded-lg border border-white/10">
                        <EMAIL>
                      </code>
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                      <span className="text-sm font-medium text-blue-300 min-w-[5rem]">Password:</span>
                      <code className="text-sm text-white bg-black/20 px-3 py-1 rounded-lg border border-white/10">
                        Test@1234
                      </code>
                    </div>
                  </div>
                </div>

                {/* Footer Links */}
                <div className="text-center mt-6">
                  <p className="text-sm text-blue-300">
                    Need access?{' '}
                    <Link
                      to="/contact"
                      className="text-blue-200 hover:text-white font-medium transition-colors duration-200"
                    >
                      Contact Support
                    </Link>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login; 