import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { FaShieldAlt, FaLock, FaEye, FaEyeSlash, FaArrowLeft, FaInfoCircle, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';
import { useResetPasswordMutation } from '../features/auth/authApi';
import { showToast, ToastContainer } from '../utils/toast';
import { cn } from '../utils/cn';

const ResetPassword = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordReset, setPasswordReset] = useState(false);
  const [searchParams] = useSearchParams();
  const [resetPassword, { isLoading, error }] = useResetPasswordMutation();
  const navigate = useNavigate();

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      showToast('error', 'Invalid or missing reset token.');
      navigate('/login');
    }
  }, [token, navigate]);

  const validatePassword = (pwd) => {
    const minLength = pwd.length >= 8;
    const hasUpper = /[A-Z]/.test(pwd);
    const hasLower = /[a-z]/.test(pwd);
    const hasNumber = /\d/.test(pwd);
    const hasSpecial = /[!@#$%^&*]/.test(pwd);
    
    return {
      minLength,
      hasUpper,
      hasLower,
      hasNumber,
      hasSpecial,
      isValid: minLength && hasUpper && hasLower && hasNumber && hasSpecial
    };
  };

  const passwordValidation = validatePassword(password);
  const passwordsMatch = password === confirmPassword && confirmPassword !== '';

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!passwordValidation.isValid) {
      showToast('error', 'Please ensure your password meets all requirements.');
      return;
    }
    
    if (!passwordsMatch) {
      showToast('error', 'Passwords do not match.');
      return;
    }

    try {
      const result = await resetPassword({ 
        token, 
        newPassword: password,
        confirmPassword: confirmPassword 
      }).unwrap();
      setPasswordReset(true);
      showToast('success', 'Password has been reset successfully.');
    } catch (err) {
      showToast('error', err?.data?.message || 'Failed to reset password. Please try again.');
    }
  };

  if (passwordReset) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 text-center">
            <div className="mb-6">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                <FaCheckCircle className="text-3xl text-green-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3">Password Reset Successful</h3>
              <p className="text-gray-600 mb-6">
                Your password has been successfully updated. You can now log in with your new password.
              </p>
            </div>
            <Link
              to="/login"
              className="w-full inline-flex items-center justify-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
            >
              Go to Login
            </Link>
          </div>
        </div>
        <ToastContainer position="top-right" autoClose={3000} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left Side - Background */}
      <div className="hidden lg:flex lg:w-7/12 bg-gradient-to-br from-purple-600 via-indigo-700 to-blue-800 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 flex items-center justify-center p-12">
          <div className="text-white max-w-lg">
            <h1 className="text-5xl font-bold mb-6 flex items-center">
              <FaShieldAlt className="mr-4 text-purple-200" />
              Log Management System
            </h1>
            <p className="text-xl mb-6 text-purple-100">
              Create a new secure password for your account.
            </p>
            <div className="flex items-center text-purple-100">
              <FaInfoCircle className="mr-3 text-purple-300 flex-shrink-0" />
              <span>Choose a strong password to keep your account secure.</span>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Reset Password Form */}
      <div className="w-full lg:w-5/12 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <div className="lg:hidden mb-6">
              <h2 className="text-2xl font-bold text-purple-600 flex items-center justify-center">
                <FaShieldAlt className="mr-2" />
                Log Management System
              </h2>
            </div>
            <h3 className="text-3xl font-bold text-gray-900 mb-2">Reset Password</h3>
            <p className="text-gray-600">
              Please enter your new password below.
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center">
              <FaExclamationTriangle className="text-red-500 mr-3 flex-shrink-0" />
              <span className="text-red-700">{error?.data?.message || 'Failed to reset password. Please try again.'}</span>
            </div>
          )}

          {/* Reset Password Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* New Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                New Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="text-gray-400" />
                </div>
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  placeholder="Enter new password"
                  value={password}
                  onChange={e => setPassword(e.target.value)}
                  required
                  className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200 placeholder-gray-400"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(v => !v)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none"
                >
                  {showPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
            </div>

            {/* Confirm Password Field */}
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                Confirm Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="text-gray-400" />
                </div>
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  id="confirmPassword"
                  name="confirmPassword"
                  placeholder="Confirm new password"
                  value={confirmPassword}
                  onChange={e => setConfirmPassword(e.target.value)}
                  required
                  className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200 placeholder-gray-400"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(v => !v)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 focus:outline-none"
                >
                  {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
            </div>

            {/* Password Requirements */}
            {password && (
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-sm">
                  <div className="font-medium text-gray-900 mb-3">Password Requirements:</div>
                  <div className="space-y-2">
                    <div className={cn(
                      "flex items-center text-sm",
                      passwordValidation.minLength ? 'text-green-600' : 'text-gray-500'
                    )}>
                      <span className="mr-2">{passwordValidation.minLength ? '✓' : '○'}</span>
                      At least 8 characters
                    </div>
                    <div className={cn(
                      "flex items-center text-sm",
                      passwordValidation.hasUpper ? 'text-green-600' : 'text-gray-500'
                    )}>
                      <span className="mr-2">{passwordValidation.hasUpper ? '✓' : '○'}</span>
                      One uppercase letter
                    </div>
                    <div className={cn(
                      "flex items-center text-sm",
                      passwordValidation.hasLower ? 'text-green-600' : 'text-gray-500'
                    )}>
                      <span className="mr-2">{passwordValidation.hasLower ? '✓' : '○'}</span>
                      One lowercase letter
                    </div>
                    <div className={cn(
                      "flex items-center text-sm",
                      passwordValidation.hasNumber ? 'text-green-600' : 'text-gray-500'
                    )}>
                      <span className="mr-2">{passwordValidation.hasNumber ? '✓' : '○'}</span>
                      One number
                    </div>
                    <div className={cn(
                      "flex items-center text-sm",
                      passwordValidation.hasSpecial ? 'text-green-600' : 'text-gray-500'
                    )}>
                      <span className="mr-2">{passwordValidation.hasSpecial ? '✓' : '○'}</span>
                      One special character (!@#$%^&*)
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Password Match Indicator */}
            {confirmPassword && (
              <div className={cn(
                "text-sm font-medium",
                passwordsMatch ? 'text-green-600' : 'text-red-600'
              )}>
                {passwordsMatch ? '✓ Passwords match' : '✗ Passwords do not match'}
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading || !passwordValidation.isValid || !passwordsMatch}
              className={cn(
                "w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2",
                (isLoading || !passwordValidation.isValid || !passwordsMatch) && "opacity-75 cursor-not-allowed"
              )}
            >
              <FaLock className="mr-2" />
              {isLoading ? 'Resetting...' : 'Reset Password'}
            </button>
          </form>

          {/* Back to Login */}
          <div className="text-center mt-6">
            <Link 
              to="/login" 
              className="inline-flex items-center text-purple-600 hover:text-purple-700 font-medium transition-colors duration-200"
            >
              <FaArrowLeft className="mr-2" />
              Back to Login
            </Link>
          </div>

          {/* Info Alert */}
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mt-6">
            <h6 className="font-medium text-purple-900 mb-2 flex items-center">
              <FaInfoCircle className="mr-2" />
              Security Tips
            </h6>
            <p className="text-purple-700 text-sm">
              Use a unique password that you don't use for other accounts. Consider using a password manager.
            </p>
          </div>
        </div>
      </div>
      <ToastContainer position="top-right" autoClose={3000} />
    </div>
  );
};

export default ResetPassword;