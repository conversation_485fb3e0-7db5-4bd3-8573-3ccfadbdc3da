@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }
  
  body {
    @apply bg-white text-gray-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Global scrollbar styling */
  html {
    scrollbar-width: thin;
    scrollbar-color: #3b82f6 transparent;
  }
  
  html::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  html::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }
  
  html::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #3b82f6, #06b6d4);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  html::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #2563eb, #0891b2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  body::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  body::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }
  
  body::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #3b82f6, #06b6d4);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  body::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #2563eb, #0891b2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

@layer components {
  /* Custom component styles */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-white;
  }
  
  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }
  
  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }
  
  .btn-warning {
    @apply bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-400;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .btn-outline {
    @apply border border-gray-300 bg-transparent hover:bg-gray-50 hover:text-gray-900;
  }
  
  .card {
    @apply rounded-lg border bg-white text-gray-900 shadow-sm;
  }
  
  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }
  
  .card-body {
    @apply p-6 pt-0;
  }
  
  .form-input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-transparent px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .form-label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }
  
  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }
  
  .badge-primary {
    @apply border-transparent bg-blue-600 text-white hover:bg-blue-700;
  }
  
  .badge-secondary {
    @apply border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200;
  }
  
  .badge-success {
    @apply border-transparent bg-green-100 text-green-800 hover:bg-green-200;
  }
  
  .badge-warning {
    @apply border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200;
  }
  
  .badge-danger {
    @apply border-transparent bg-red-100 text-red-800 hover:bg-red-200;
  }
  
  .table {
    @apply w-full caption-bottom text-sm;
  }
  
  .table-header {
    @apply border-b;
  }
  
  .table-row {
    @apply border-b transition-colors hover:bg-gray-50 data-[state=selected]:bg-gray-100;
  }
  
  .table-cell {
    @apply p-4 align-middle;
  }
  
  .table-head {
    @apply h-12 px-4 text-left align-middle font-medium text-gray-500;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }
  
  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }
  
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
  
  /* Auto-fill override utilities */
  .autofill-transparent {
    -webkit-box-shadow: 0 0 0 1000px rgba(255, 255, 255, 0.1) inset !important;
    -webkit-text-fill-color: white !important;
    caret-color: white !important;
  }
  
  .autofill-transparent:autofill {
    -webkit-box-shadow: 0 0 0 1000px rgba(255, 255, 255, 0.1) inset !important;
    -webkit-text-fill-color: white !important;
    transition: background-color 5000s ease-in-out 0s !important;
  }
  
  /* Custom Scrollbar Styles */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #3b82f6, #06b6d4);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #2563eb, #0891b2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .custom-scrollbar::-webkit-scrollbar-corner {
    background: rgba(255, 255, 255, 0.1);
  }
  
  /* Firefox scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #3b82f6 transparent;
  }
  
  /* Dark theme scrollbar variant */
  .custom-scrollbar-dark::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  .custom-scrollbar-dark::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }
  
  .custom-scrollbar-dark::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #60a5fa, #22d3ee);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .custom-scrollbar-dark::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #3b82f6, #06b6d4);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  .custom-scrollbar-dark {
    scrollbar-width: thin;
    scrollbar-color: #60a5fa transparent;
  }
}

/* Keyframes for custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
