import { configureStore } from '@reduxjs/toolkit';
import userTokenReducer from '../features/auth/userTokenReducer';
import authApi from '../features/auth/authApi';
import projectApi from '../features/projects/projectApi';
import roleApi from '../features/role/roleApi';
import commonApi from '../features/common/commonApi';
import storage from 'redux-persist/lib/storage';
import { persistReducer, persistStore } from 'redux-persist';

const persistConfig = {
  key: 'auth',
  storage,
  whitelist: ['access_token', 'data'],
};

const persistedAuthReducer = persistReducer(persistConfig, userTokenReducer);

export const store = configureStore({
  reducer: {
    auth: persistedAuthReducer,
    [authApi.reducerPath]: authApi.reducer,
    [projectApi.reducerPath]: projectApi.reducer,
    [roleApi.reducerPath]: roleApi.reducer,
    [commonApi.reducerPath]: commonApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }).concat(authApi.middleware, projectApi.middleware, roleApi.middleware, commonApi.middleware),
});

export const persistor = persistStore(store); 