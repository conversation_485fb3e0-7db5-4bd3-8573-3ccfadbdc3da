import * as Yup from 'yup';
import { errorMessages } from './errorMessages';

export const loginValidationSchema = Yup.object().shape({
  email: Yup.string()
    .trim()
    .required(errorMessages.EMAIL)
    .matches(/^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/, errorMessages.EMAIL_MATCHES),
  password: Yup.string()
    .trim()
    .required(errorMessages.PASSWORD)
    .matches(/^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]).{8,}$/, errorMessages.PASSWORD_MATCHES),
}); 