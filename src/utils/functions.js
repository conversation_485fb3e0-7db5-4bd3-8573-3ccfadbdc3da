// import { <PERSON><PERSON><PERSON> } from "buffer";

// import moment from "moment-timezone";
import { toast } from "react-toastify";
// import Swal from "sweetalert2";

// import "react-toastify/dist/ReactToastify.css";

// import blogimg from "assets/images/blog-img.webp";
// import favicon from "assets/images/indianic-short-logo.svg";
// import logo from "assets/images/logo.svg";
// import profilepic from "assets/images/profile-pic.jpg";
import generalConfig from "../configs/generalConfig";
// import { DEFAULT_DATE_FORMAT, DEFAULT_LANGUAGE_CODE } from "utils/constants";
// import { localStorageKeys } from "utils/localStorageKeys";

/******************* 
@Purpose : Used for show message notification
@Parameter : text, type, autoClose, position
<AUTHOR> INIC
******************/
export const messageNotification = (
  text,
  type = "success",
  autoClose = generalConfig.TOAST_TIMER,
  position = "top-right"
) => {
  toast[type](text, {
    position: position,
    autoClose: autoClose,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
  });
};

// /******************* 
// @purpose : Used for encode data into base 64
// @Parameter : {data} 
// <AUTHOR> INIC
// ******************/
// export const encodeBase64 = (data) => {
//   return Buffer.from(data).toString("base64");
// };

// /******************* 
// @purpose : Used for decode data from base 64
// @Parameter : {data} 
// <AUTHOR> INIC
// ******************/
// export const decodeBase64 = (data) => {
//   return Buffer.from(data, "base64").toString("ascii");
// };

// /******************* 
// @purpose : Used for content type
// @Parameter : {} 
// <AUTHOR> IndiaNIC
// ******************/
// export const getHeader = (formData = false) => {
//   return {
//     "Content-Type": formData ? "multipart/form-data" : "application/json",
//     Accept: "application/json",
//   };
// };

// /******************* 
// @purpose : Used for File upload extension
// @Parameter : {} 
// <AUTHOR> INIC
// ******************/
// export const FILE_UPLOAD_EXTENSION = [
//   "image/png", // for .png image
//   "image/jpeg", // for .jpeg image
//   "image/jpg", // for .jpg image
//   // ".svg", // for .svg image
// ];

// /******************* 
// @purpose : Used for Remember Me Login
// @Parameter : {} 
// <AUTHOR> INIC
// ******************/

// export const rememberMe = (clearTheme = true) => {
//   const rememberMe = localStorage?.getItem(localStorageKeys.REMEMBER_ME);
//   if (!rememberMe) {
//     localStorage.clear();
//   } else {
//     if (clearTheme) {
//       localStorage.removeItem(localStorageKeys.THEME);
//     }
//     localStorage.removeItem(localStorageKeys.EXPIRATIONTIME);
//     localStorage.removeItem(localStorageKeys.IS_EMAIL);
//     localStorage.removeItem(localStorageKeys.IS_MOBILE);
//     localStorage.removeItem(localStorageKeys.ma_la);
//     localStorage.removeItem(localStorageKeys.MASKED_EMAIL);
//     localStorage.removeItem(localStorageKeys.MASKED_PHONE);
//     localStorage.removeItem(localStorageKeys.OTP_TOKEN);
//     localStorage.removeItem(localStorageKeys?.VERIFY_LOGIN_TOKEN);
//   }
// };

// /******************* 
// @purpose : Used for show default profile image onerror
// @Parameter : {} 
// <AUTHOR> INIC
// ******************/

// export const setDefaultProfilePic = (e) => {
//   e.currentTarget.src = profilepic;
//   e.currentTarget.onerror = null;
// };

// /******************* 
// @purpose : Used for show default profile image onerror
// @Parameter : {} 
// <AUTHOR> INIC
// ******************/

// export const setDefaultLogo = (e) => {
//   e.currentTarget.src = logo;
//   e.currentTarget.onerror = null;
// };
// export const setDefaultFavicon = (e) => {
//   e.currentTarget.src = favicon;
//   e.currentTarget.onerror = null;
// };

// export const arrayToJson = (array, key) => {
//   let data = {};
//   for (let obj of array) {
//     data[obj[key]] = obj;
//   }
//   return data;
// };

// export const jsonToArray = (obj) => Object.values(obj);

// /******************* 
// @purpose : Used for subtracting days from a given date
// @Parameter : {dateString, daysToSubtract} 
// <AUTHOR> Cascade
// ******************/
// export const subtractDaysToDate = (dateString, daysToSubtract) => {
//   if (!dateString || typeof daysToSubtract !== "number") {
//     return null; // Or handle error appropriately
//   }
//   return moment(dateString)
//     .subtract(daysToSubtract, "days")
//     .format("YYYY-MM-DD");
// };

// /******************* 
// @purpose : Used for formatting date 
// @Parameter : {date, dateFormat} 
// <AUTHOR> INIC
// ******************/
// export const getFormattedDate = (date, dateFormat) => {
//   if (date === "") {
//     return false;
//   }
//   if (dateFormat) {
//     return moment(date).format(dateFormat);
//   } else {
//     return moment(date).format(DEFAULT_DATE_FORMAT);
//   }
// };

// /******************* 
// @purpose : Used for formatting date in date picker date format prop
// @Parameter : {date, dateFormat} 
// <AUTHOR> INIC
// ******************/
// export const getFormattedDateWithTime = (dateFormat, timeFormat) => {
//   if (timeFormat === "12 hours") {
//     return `${dateFormat} h:mm aa`;
//   } else if (timeFormat === "24 hours") {
//     return `${dateFormat} HH:mm`;
//   } else {
//     return `${dateFormat} h:mm aa`;
//   }
// };

// /******************* 
// @purpose : Used for converting date according to timezone
// @Parameter : {date, timezone} 
// <AUTHOR> INIC
// ******************/
// export const getTimeZoneConvertedDate = (date, timezone) => {
//   if (date === "") {
//     return false;
//   }
//   return moment.tz(date, timezone);
// };

// /******************* 
// @purpose : Used for converting date to utc
// @Parameter : {date} 
// <AUTHOR> INIC
// ******************/
// export const getUTCFormat = (date) => {
//   return moment(date).utc().format("YYYY-MM-DDTHH:mm:ss");
// };

// export const getFormattedTimeWithTimezone = (time, timezone, timeFormat) => {
//   if (timeFormat === "12 hours") {
//     return moment(time).tz(timezone).format("hh:mm A");
//   } else if (timeFormat === "24 hours") {
//     return moment(time).tz(timezone).format("HH:mm");
//   } else {
//     return moment(time).tz(timezone).format("hh:mm A");
//   }
// };

// export const getFormattedDateTimeWithTimezone = (
//   date,
//   timezone = "UTC",
//   dateFormat = "YYYY-MM-DD",
//   timeFormat = "12 hours"
// ) => {
//   const validTimezone = timezone.includes("Asia/") ? timezone : "Asia/Kolkata"; // or handle gracefully

//   if (timeFormat === "12 hours") {
//     return moment(date).tz(validTimezone).format(`${dateFormat} hh:mm A`);
//   } else if (timeFormat === "24 hours") {
//     return moment(date).tz(validTimezone).format(`${dateFormat} HH:mm`);
//   } else {
//     return moment(date).tz(validTimezone).format(`${dateFormat} hh:mm A`);
//   }
// };

// /******************* 
// @purpose : Used for show alert popup 
// @Parameter : {} 
// <AUTHOR> INIC
// ******************/
// export const fireDeletePopup = async (
//   confirmDeleteHandler,
//   resetGridRowSelection,
//   title
// ) => {
//   const willDelete = await Swal.fire({
//     title: title || "Are you sure, you want to delete?",
//     iconHtml:
//       "<span class='inic inic-trash' style='font-size: 40px; color: #ff3333;'></span>",
//     buttons: true,
//     dangerMode: true,
//     showCancelButton: true,
//     confirmButtonText: "Delete",
//     customClass: {
//       icon: "swal-custom-icon",
//       title: "swal-custom-title",
//       htmlContainer: "swal-custom-html",
//       confirmButton: "swal-custom-confirm",
//       cancelButton: "swal-custom-cancel",
//     },
//     reverseButtons: true,
//     focusConfirm: false,
//     focusCancel: true,
//     didOpen: () => {
//       const style = document.createElement("style");
//       style.innerHTML = `
//         .swal-custom-icon {
//           background-color: #ffe6e6;
//           border: 1px solid #ff3333;
//           border-radius: 50%;
//           width: 80px;
//           height: 80px;
//           display: flex;
//           align-items: center;
//           justify-content: center;
//         }
//         .swal-custom-title {
//           font-size: 24px;
//           font-weight: 600;
//           color: #333;
//         }
//         .swal-custom-html {
//           color: #666;
//           font-size: 16px;
//         }
//       `;
//       document.head.appendChild(style);
//     },
//   });

//   if (willDelete.isConfirmed) {
//     confirmDeleteHandler();
//     resetGridRowSelection && resetGridRowSelection();
//   }
// };
// export const fireAutoUpdatePopup = async (
//   confirmDeleteHandler,
//   resetGridRowSelection,
//   title
// ) => {
//   const willAutoUpdate = await Swal.fire({
//     title: title || "Are you sure, you want to auto update?",
//     iconHtml:
//       "<span class='inic inic-refresh' style='font-size: 40px; color: #F2AA30;'></span>",
//     buttons: true,
//     dangerMode: true,
//     showCancelButton: true,
//     confirmButtonText: "Auto Update",
//     customClass: {
//       icon: "swal-custom-icon",
//       title: "swal-custom-title",
//       htmlContainer: "swal-custom-html",
//       confirmButton: "swal-custom-confirm",
//       cancelButton: "swal-custom-cancel",
//     },
//     reverseButtons: true,
//     focusConfirm: false,
//     focusCancel: true,
//     didOpen: () => {
//       const style = document.createElement("style");
//       style.innerHTML = `
//         .swal-custom-icon {
//           background-color: #FCEED6;
//           border: 1px solid #F2AA30;
//           border-radius: 50%;
//           width: 80px;
//           height: 80px;
//           display: flex;
//           align-items: center;
//           justify-content: center;
//         }
//         .swal-custom-title {
//           font-size: 24px;
//           font-weight: 600;
//           color: #333;
//         }
//         .swal-custom-html {
//           color: #666;
//           font-size: 16px;
//         }
//       `;
//       document.head.appendChild(style);
//     },
//   });

//   if (willAutoUpdate.isConfirmed) {
//     confirmDeleteHandler();
//     resetGridRowSelection && resetGridRowSelection();
//   }
// };
// export const fireArchivePopup = async (
//   confirmDeleteHandler,
//   resetGridRowSelection,
//   title
// ) => {
//   const willDelete = await Swal.fire({
//     title: title || "Are you sure, you want to archive?",
//     iconHtml:
//       "<span class='inic inic-archive' style='font-size: 40px; color: #ff9966;'></span>",
//     buttons: true,
//     dangerMode: true,
//     showCancelButton: true,
//     confirmButtonText: "Archive",
//     customClass: {
//       icon: "swal-custom-icon",
//       title: "swal-custom-title",
//       htmlContainer: "swal-custom-html",
//       confirmButton: "swal-custom-confirm",
//       cancelButton: "swal-custom-cancel",
//     },
//     reverseButtons: true,
//     focusConfirm: false,
//     focusCancel: true,
//     didOpen: () => {
//       const style = document.createElement("style");
//       style.innerHTML = `
//         .swal-custom-icon {
//           background-color:rgb(248, 237, 232);
//           border: 1px solid #ff9966;
//           border-radius: 50%;
//           width: 80px;
//           height: 80px;
//           display: flex;
//           align-items: center;
//           justify-content: center;
//         }
//         .swal-custom-title {
//           font-size: 24px;
//           font-weight: 600;
//           color: #333;
//         }
//         .swal-custom-html {
//           color: #666;
//           font-size: 16px;
//         }
//       `;
//       document.head.appendChild(style);
//     },
//   });

//   if (willDelete.isConfirmed) {
//     confirmDeleteHandler();
//     resetGridRowSelection && resetGridRowSelection();
//   }
// };
// export const fireDeletePopupTrash = async (
//   confirmDeleteHandler,
//   resetGridRowSelection,
//   title,
//   name
// ) => {
//   const willDelete = await Swal.fire({
//     title: title || "Move to Trash",
//     html: `Are you sure you want to permanently delete ${name}?`,
//     iconHtml:
//       "<span class='inic inic-trash' style='font-size: 40px; color: #ff3333;'></span>",
//     customClass: {
//       icon: "swal-custom-icon",
//       title: "swal-custom-title",
//       htmlContainer: "swal-custom-html",
//       confirmButton: "swal-custom-confirm",
//       cancelButton: "swal-custom-cancel",
//     },
//     showCancelButton: true,
//     confirmButtonText: "Move to Trash",
//     cancelButtonText: "Cancel",
//     reverseButtons: true,
//     focusConfirm: false,
//     focusCancel: true,
//     didOpen: () => {
//       const style = document.createElement("style");
//       style.innerHTML = `
//         .swal-custom-icon {
//           background-color: #ffe6e6;
//           border: 1px solid #ff3333;
//           border-radius: 50%;
//           width: 80px;
//           height: 80px;
//           display: flex;
//           align-items: center;
//           justify-content: center;
//         }
//         .swal-custom-title {
//           font-size: 24px;
//           font-weight: 600;
//           color: #333;
//         }
//         .swal-custom-html {
//           color: #666;
//           font-size: 16px;
//         }
//       `;
//       document.head.appendChild(style);
//     },
//   });

//   if (willDelete.isConfirmed) {
//     confirmDeleteHandler();
//     resetGridRowSelection && resetGridRowSelection();
//   }
// };
// export const getLanguageOptions = (data) => {
//   return data?.map((list) => ({
//     label: `${list?.language}(${list?.country})`,
//     value: list?._id,
//   }));
// };

// export const getLanguageCode = (data, id) => {
//   return id && data
//     ? data?.find((list) => list?._id === id)?.languageCode
//     : DEFAULT_LANGUAGE_CODE;
// };

// /******************* 
// @purpose : Used for setting default pic in blog card view 
// @Parameter : {} 
// <AUTHOR> INIC
// ******************/
// export const setDefaultBlogImage = (e) => {
//   e.currentTarget.src = blogimg;
//   e.currentTarget.onerror = null;
// };
// /******************* 
// @purpose : Used for select Option in react select 
// @Parameter : {} 
// <AUTHOR> INIC
// ******************/
// export const selectOptionHandler = (formik, name) => {
//   return formik?.values?.[name]
//     ? {
//         label: formik?.values?.[name],
//         value: formik?.values?.[name],
//       }
//     : undefined;
// };

// /******************* 
// @purpose : Used for conditional ternary 
// @Parameter : {} 
// <AUTHOR> INIC
// ******************/
// export const conditionalTernaryHandler = (condition, param1, param2) => {
//   return condition ? param1 : param2;
// };

// /******************* 
// @purpose : Used for common ternary 
// @Parameter : {} 
// <AUTHOR> INIC
// ******************/
// export const commonTernaryHandler = (param, param2) => {
//   return param ? param : param2;
// };

// /******************* 
// @purpose : Used for download file
// @Parameter : {type,file} 
// <AUTHOR> INIC
// ******************/
// export const downlaodFiletoType = (filepath) => {
//   const fileName = filepath.split("/").pop();

//   const link = document.createElement("a");
//   link.href = filepath;
//   link.setAttribute("download", fileName);
//   document.body.appendChild(link);
//   link.click();
//   link.remove();
// };

// /******************* 
// @purpose : Used for sorting array of objects
// @Parameter : {arr, key} 
// <AUTHOR> INIC
// ******************/
// export const sortArrOfObj = (arr, key) => {
//   return arr.sort((a, b) => {
//     if (a[key] < b[key]) {
//       return -1;
//     }
//     if (a[key] > b[key]) {
//       return 1;
//     }
//     return 0;
//   });
// };

// export const formatUrlPathScopes = (pathname, params, permission) => {
//   // remove any params from url
//   const pathWithoutParams = Object.values(params).reduce(
//     (path, param) => path.replace("/" + param, ""),
//     pathname
//   );

//   // get crud scope from url (i.e view/create/edit)
//   const pathScope = Object.values(permission).find(
//     (scope) => pathWithoutParams.indexOf(scope.toLowerCase()) != -1
//   );

//   // get url module path
//   const pathWithoutScope = pathScope
//     ? pathWithoutParams.replace("/" + pathScope.toLowerCase(), "")
//     : pathWithoutParams;

//   return { pathWithoutParams, pathScope, pathWithoutScope };
// };

// export const initialValues = {
//   save: false,
//   saveFilterSelect: "",
//   filter: [
//     {
//       key: "",
//       type: "",
//       value: "",
//       condition: "$and",
//     },
//   ],
//   filterName: "",
//   isApplied: false,
// };

// export const formatYearMonth = (dateString) => {
//   const [year, month] = dateString?.split("-");
//   const monthNames = [
//     "Jan",
//     "Feb",
//     "Mar",
//     "Apr",
//     "May",
//     "Jun",
//     "Jul",
//     "Aug",
//     "Sep",
//     "Oct",
//     "Nov",
//     "Dec",
//   ];

//   const formattedMonth = monthNames[parseInt(month, 10) - 1];
//   return `${formattedMonth} ${year}`;
// };

// export const fireDiscardPopup = async (onDiscardConfirmed) => {
//   const willDiscard = await Swal.fire({
//     title: "Reset Changes?",
//     text: "Any unsaved changes will be lost. Are you sure?",
//     icon: "warning",
//     showCancelButton: true,
//     confirmButtonText: "Reset",
//     cancelButtonText: "Cancel",
//     reverseButtons: true,
//     focusConfirm: false,
//     focusCancel: true,
//   });

//   if (willDiscard.isConfirmed) {
//     onDiscardConfirmed();
//   }
// };

// export const capitalizeFirstLetter = (str) => {
//   return str ? str.charAt(0).toUpperCase() + str.slice(1) : "";
// };
// export const firePublishConfirmationPopup = async (
//   confirmDeleteHandler,
//   title,
//   publishText
// ) => {
//   const willPublish = await Swal.fire({
//     title: title || "Are you sure, you want to publish?",
//     icon: "warning",
//     buttons: true,
//     dangerMode: true,
//     showCancelButton: true,
//     confirmButtonText: publishText || "Publish",
//     cancelButtonText: "Cancel",
//     reverseButtons: true,
//     focusConfirm: false,
//     focusCancel: true,
//   });

//   if (willPublish.isConfirmed) {
//     confirmDeleteHandler();
//   }
// };
// export const formatTypeLabel = (str) => {
//   return str?.replace(/([a-z])([A-Z])/g, "$1 $2") || "-";
// };
