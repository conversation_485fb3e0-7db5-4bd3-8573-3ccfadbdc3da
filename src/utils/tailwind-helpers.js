/**
 * Tailwind CSS Helper Utilities for Log Management System
 * Use these pre-defined classes for consistent styling across the application
 */

// Common button styles
export const buttonStyles = {
  primary: "bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
  secondary: "bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",
  success: "bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",
  warning: "bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2",
  danger: "bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",
  outline: "border border-gray-300 bg-transparent hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",
  ghost: "hover:bg-gray-100 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",
  small: "py-1 px-3 text-sm",
  large: "py-3 px-6 text-lg"
};

// Badge/Status styles
export const badgeStyles = {
  primary: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",
  secondary: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",
  success: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",
  warning: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",
  danger: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
};

// Card styles
export const cardStyles = {
  base: "bg-white rounded-lg shadow-sm border border-gray-200",
  hover: "bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",
  interactive: "bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md hover:border-gray-300 transition-all duration-200 cursor-pointer",
  header: "px-6 py-4 border-b border-gray-200",
  body: "p-6",
  footer: "px-6 py-4 border-t border-gray-200 bg-gray-50"
};

// Form input styles
export const inputStyles = {
  base: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
  error: "w-full px-3 py-2 border border-red-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent",
  success: "w-full px-3 py-2 border border-green-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",
  small: "px-2 py-1 text-sm",
  large: "px-4 py-3 text-lg"
};

// Table styles
export const tableStyles = {
  wrapper: "overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg",
  table: "min-w-full divide-y divide-gray-300",
  header: "bg-gray-50",
  headerCell: "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
  body: "bg-white divide-y divide-gray-200",
  row: "hover:bg-gray-50",
  cell: "px-6 py-4 whitespace-nowrap text-sm text-gray-900"
};

// Layout styles
export const layoutStyles = {
  container: "w-full px-6", // Full width container
  containerCentered: "container mx-auto px-4 max-w-7xl", // Centered container (for forms, etc.)
  section: "py-6",
  grid: {
    cols1: "grid grid-cols-1 gap-6",
    cols2: "grid grid-cols-1 md:grid-cols-2 gap-6",
    cols3: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
    cols4: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
  }
};

// Status colors for HTTP methods, response codes, etc.
export const statusColors = {
  http: {
    GET: "bg-blue-100 text-blue-800",
    POST: "bg-green-100 text-green-800",
    PUT: "bg-yellow-100 text-yellow-800",
    DELETE: "bg-red-100 text-red-800",
    PATCH: "bg-purple-100 text-purple-800"
  },
  response: {
    success: "bg-green-100 text-green-800", // 2xx
    redirect: "bg-blue-100 text-blue-800",  // 3xx
    clientError: "bg-yellow-100 text-yellow-800", // 4xx
    serverError: "bg-red-100 text-red-800" // 5xx
  },
  status: {
    active: "bg-green-100 text-green-800",
    inactive: "bg-gray-100 text-gray-800",
    pending: "bg-yellow-100 text-yellow-800",
    expired: "bg-red-100 text-red-800"
  }
};

// Animation classes
export const animations = {
  fadeIn: "animate-fade-in",
  slideIn: "animate-slide-in",
  spin: "animate-spin",
  pulse: "animate-pulse",
  bounce: "animate-bounce"
};

// Spacing utilities
export const spacing = {
  xs: "p-2",
  sm: "p-4",
  md: "p-6",
  lg: "p-8",
  xl: "p-12"
};

// Helper function to get HTTP method color
export const getHttpMethodColor = (method) => {
  return statusColors.http[method?.toUpperCase()] || statusColors.http.GET;
};

// Helper function to get response code color
export const getResponseCodeColor = (code) => {
  if (code >= 200 && code < 300) return statusColors.response.success;
  if (code >= 300 && code < 400) return statusColors.response.redirect;
  if (code >= 400 && code < 500) return statusColors.response.clientError;
  if (code >= 500) return statusColors.response.serverError;
  return statusColors.response.clientError;
};

// Helper function to get user role color
export const getUserRoleColor = (role) => {
  switch (role?.toLowerCase()) {
    case 'super admin':
      return statusColors.http.GET; // blue
    case 'admin':
      return statusColors.response.success; // green
    default:
      return statusColors.status.inactive; // gray
  }
}; 