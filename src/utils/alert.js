import Swal from 'sweetalert2';

export function confirmDialog({
  title = 'Are you sure?',
  text = 'This action cannot be undone.',
  icon = 'warning',
  confirmButtonText = 'Yes',
  cancelButtonText = 'Cancel',
  ...rest
} = {}) {
  return Swal.fire({
    title,
    text,
    icon,
    showCancelButton: true,
    confirmButtonColor: '#2563eb',
    cancelButtonColor: '#6c757d',
    confirmButtonText,
    cancelButtonText,
    ...rest,
  }).then(result => result.isConfirmed);
} 