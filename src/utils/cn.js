import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Utility function to merge Tailwind CSS classes
 * @param {...string} inputs - Class names to merge
 * @returns {string} - Merged class names
 */
export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

/**
 * Button variant styles for consistent styling
 */
export const buttonVariants = {
  primary: "bg-primary-600 hover:bg-primary-700 text-white",
  secondary: "bg-secondary-100 hover:bg-secondary-200 text-secondary-900",
  success: "bg-success-600 hover:bg-success-700 text-white",
  warning: "bg-warning-500 hover:bg-warning-600 text-white",
  danger: "bg-danger-600 hover:bg-danger-700 text-white",
  outline: "border border-gray-300 bg-transparent hover:bg-gray-50 text-gray-700",
  ghost: "hover:bg-gray-100 text-gray-700",
};

/**
 * Badge variant styles
 */
export const badgeVariants = {
  primary: "bg-primary-100 text-primary-800 border-primary-200",
  secondary: "bg-secondary-100 text-secondary-800 border-secondary-200",
  success: "bg-success-100 text-success-800 border-success-200",
  warning: "bg-warning-100 text-warning-800 border-warning-200",
  danger: "bg-danger-100 text-danger-800 border-danger-200",
  info: "bg-blue-100 text-blue-800 border-blue-200",
};

/**
 * Input variant styles
 */
export const inputVariants = {
  default: "border-gray-300 focus:border-primary-500 focus:ring-primary-500",
  error: "border-danger-300 focus:border-danger-500 focus:ring-danger-500",
  success: "border-success-300 focus:border-success-500 focus:ring-success-500",
};
